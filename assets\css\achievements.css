/**
 * Achievement Styles
 * MLM Binary Plan System
 */

/* Level Badges */
.level-badge {
    display: inline-block;
    padding: 0.375rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.level-badge.level-beginner {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.level-badge.level-intermediate {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
}

.level-badge.level-expert {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    color: white;
}

/* Award Badges */
.award-badge {
    display: inline-block;
    padding: 0.375rem 0.75rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.award-badge i {
    color: #ffd700;
}

/* Achievement Cards */
.achievement-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 2px solid transparent;
}

.achievement-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.achievement-card.has-award {
    border-color: #ffd700;
    background: linear-gradient(135deg, #fff9e6, #ffffff);
}

.achievement-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 1rem;
    position: relative;
}

.achievement-icon.level-beginner {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.achievement-icon.level-intermediate {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
}

.achievement-icon.level-expert {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    color: white;
}

.achievement-icon.award {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #ffd700;
}

/* Achievement Timeline */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #667eea, #764ba2);
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
    padding-left: 2rem;
}

.timeline-marker {
    position: absolute;
    left: -2rem;
    top: 0;
    width: 2rem;
    height: 2rem;
    background: white;
    border: 3px solid #667eea;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
}

.timeline-content {
    background: white;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #667eea;
}

/* User Dashboard Achievement Section */
.user-achievements {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
}

.user-achievements h4 {
    color: #495057;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.achievement-display {
    display: flex;
    gap: 2rem;
    align-items: center;
    flex-wrap: wrap;
}

.level-display {
    text-align: center;
}

.level-display .achievement-icon {
    margin-bottom: 0.5rem;
}

.level-display h5 {
    margin: 0;
    color: #495057;
    font-weight: 600;
}

.award-display {
    text-align: center;
    flex: 1;
}

.award-display .achievement-icon {
    margin-bottom: 0.5rem;
}

.award-display h5 {
    margin: 0;
    color: #495057;
    font-weight: 600;
}

.no-award {
    color: #6c757d;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .achievement-display {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }
    
    .timeline {
        padding-left: 1.5rem;
    }
    
    .timeline::before {
        left: 0.75rem;
    }
    
    .timeline-marker {
        left: -1.5rem;
        width: 1.5rem;
        height: 1.5rem;
    }
    
    .timeline-item {
        padding-left: 1.5rem;
    }
}

/* Animation for new achievements */
@keyframes achievementGlow {
    0% { box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
    50% { box-shadow: 0 4px 25px rgba(255, 215, 0, 0.4); }
    100% { box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
}

.achievement-card.new-achievement {
    animation: achievementGlow 2s ease-in-out infinite;
}

/* Stats Cards for Admin */
.achievement-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-card .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
    color: white;
}

.stat-card .stat-icon.levels {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.stat-card .stat-icon.awards {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.stat-card .stat-icon.recent {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
}

.stat-card h3 {
    margin: 0;
    color: #495057;
    font-weight: 700;
}

.stat-card p {
    margin: 0;
    color: #6c757d;
    font-size: 0.875rem;
}
