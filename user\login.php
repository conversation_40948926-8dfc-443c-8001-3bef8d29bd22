<?php
/**
 * User Login Page
 * MLM Binary Plan System
 */

require_once '../includes/header.php';

// Redirect if already logged in
if (isLoggedIn('user')) {
    Response::redirect('dashboard.php');
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    verifyCsrfToken();
    
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    // Validate input
    $validator = new Validator($_POST);
    $validator->required('username', 'Username is required')
             ->required('password', 'Password is required');
    
    if ($validator->passes()) {
        try {
            $db = Database::getInstance();
            $stmt = $db->prepare("SELECT id, user_id, username, email, password, full_name, phone, status FROM users WHERE username = ? OR email = ? OR user_id = ?");
            $stmt->execute([$username, $username, $username]);
            $user = $stmt->fetch();
            
            // ⚠️ CRITICAL SECURITY WARNING ⚠️
            // Using plain text password comparison - EXTREMELY DANGEROUS!
            // Original secure implementation used: password_verify($password, $user['password'])
            if ($user && $password === $user['password']) { // PLAIN TEXT COMPARISON - SECURITY RISK!
                if ($user['status'] === 'active') {
                    // Log successful login
                    $logStmt = $db->prepare("INSERT INTO login_logs (user_type, user_id, ip_address, user_agent, status) VALUES (?, ?, ?, ?, ?)");
                    $logStmt->execute(['user', $user['user_id'], getUserIP(), $_SERVER['HTTP_USER_AGENT'] ?? '', 'success']);
                    
                    // Set session
                    setUserSession('user', $user['user_id'], [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'email' => $user['email'],
                        'full_name' => $user['full_name'],
                        'phone' => $user['phone']
                    ]);
                    
                    setSuccessMessage('Welcome back, ' . $user['full_name']);
                    Response::redirect('dashboard.php');
                } else {
                    $error = 'Your account is ' . $user['status'] . '. Please contact support.';
                }
            } else {
                $error = 'Invalid credentials.';
                
                // Log failed login attempt
                if ($user) {
                    $logStmt = $db->prepare("INSERT INTO login_logs (user_type, user_id, ip_address, user_agent, status) VALUES (?, ?, ?, ?, ?)");
                    $logStmt->execute(['user', $user['user_id'], getUserIP(), $_SERVER['HTTP_USER_AGENT'] ?? '', 'failed']);
                }
            }
        } catch (Exception $e) {
            $error = 'Login failed. Please try again.';
            error_log("User login error: " . $e->getMessage());
        }
    } else {
        $error = $validator->getFirstError();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Login - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .login-header {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="login-card">
                    <div class="login-header">
                        <i class="fas fa-users fa-3x mb-3"></i>
                        <h3>User Login</h3>
                        <p class="mb-0">Access Your Dashboard</p>
                    </div>
                    <div class="card-body p-4">
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (isset($_GET['timeout'])): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-clock me-2"></i>Your session has expired. Please login again.
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="">
                            <?php echo csrfTokenInput(); ?>
                            
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-2"></i>Username, Email or User ID
                                </label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>Password
                                </label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember">
                                <label class="form-check-label" for="remember">Remember me</label>
                            </div>
                            
                            <button type="submit" class="btn btn-info w-100 mb-3">
                                <i class="fas fa-sign-in-alt me-2"></i>Login
                            </button>
                        </form>
                        
                        <div class="text-center mb-3">
                            <a href="register.php" class="btn btn-outline-info">
                                <i class="fas fa-user-plus me-2"></i>Create New Account
                            </a>
                        </div>
                        
                        <div class="text-center">
                            <a href="../index.php" class="text-muted">
                                <i class="fas fa-arrow-left me-2"></i>Back to Home
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
