<?php
/**
 * PV Manager (for testing and admin use)
 * MLM Binary Plan System
 */

require_once 'includes/header.php';
require_once 'includes/PVSystem.php';
require_once 'includes/Wallet.php';

$message = '';
$error = '';

// Handle PV addition
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_pv') {
    verifyCsrfToken();
    
    $userId = sanitizeInput($_POST['user_id']);
    $pvAmount = (float) $_POST['pv_amount'];
    $side = $_POST['side'];
    $description = sanitizeInput($_POST['description']);
    
    if ($userId && $pvAmount > 0 && in_array($side, ['left', 'right'])) {
        $pvSystem = new PVSystem();
        if ($pvSystem->addPV($userId, $pvAmount, $side, 'manual', null, null, $description, 'admin', 1)) {
            $message = "Successfully added {$pvAmount} PV to {$side} side for user {$userId}";
        } else {
            $error = "Failed to add PV";
        }
    } else {
        $error = "Invalid input data";
    }
}

// Handle manual matching
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'run_matching') {
    verifyCsrfToken();
    
    $userId = sanitizeInput($_POST['user_id']);
    
    if ($userId) {
        $pvSystem = new PVSystem();
        $pvSystem->processPVMatching($userId);
        $message = "PV matching processed for user {$userId}";
    } else {
        $error = "Invalid user ID";
    }
}

// Get user list for dropdown
$db = Database::getInstance();
$usersStmt = $db->prepare("SELECT user_id, full_name FROM users ORDER BY full_name");
$usersStmt->execute();
$users = $usersStmt->fetchAll();

// Get selected user's data
$selectedUserId = $_GET['user_id'] ?? ($_POST['user_id'] ?? '');
$userData = null;
$pvTotals = null;
$downlinePV = null;
$walletData = null;

if ($selectedUserId) {
    $userStmt = $db->prepare("SELECT * FROM users WHERE user_id = ?");
    $userStmt->execute([$selectedUserId]);
    $userData = $userStmt->fetch();
    
    if ($userData) {
        $pvSystem = new PVSystem();
        $wallet = new Wallet();
        
        $pvTotals = $pvSystem->getUserPVTotals($selectedUserId);
        $downlinePV = $pvSystem->getDownlinePVTotals($selectedUserId);
        $walletData = $wallet->getWallet($selectedUserId);
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PV Manager - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
            <img src="../assets/images/logo.png" alt="" style="height:50px; width:auto;"></i>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="tree-viewer.php">Tree Viewer</a>
                <a class="nav-link" href="index.php">Home</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-coins me-2"></i>PV Manager
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- User Selection -->
                        <form method="GET" class="mb-4">
                            <div class="row align-items-end">
                                <div class="col-md-6">
                                    <label for="user_id" class="form-label">Select User:</label>
                                    <select class="form-select" id="user_id" name="user_id" required>
                                        <option value="">Choose a user...</option>
                                        <?php foreach ($users as $user): ?>
                                            <option value="<?php echo $user['user_id']; ?>" 
                                                    <?php echo $selectedUserId === $user['user_id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($user['user_id'] . ' - ' . $user['full_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>Load User Data
                                    </button>
                                </div>
                            </div>
                        </form>
                        
                        <?php if ($userData): ?>
                            <!-- User Information -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">User Information</h5>
                                        </div>
                                        <div class="card-body">
                                            <p><strong>User ID:</strong> <?php echo htmlspecialchars($userData['user_id']); ?></p>
                                            <p><strong>Name:</strong> <?php echo htmlspecialchars($userData['full_name']); ?></p>
                                            <p><strong>Email:</strong> <?php echo htmlspecialchars($userData['email']); ?></p>
                                            <p><strong>Status:</strong> 
                                                <span class="status-badge status-<?php echo $userData['status']; ?>">
                                                    <?php echo ucfirst($userData['status']); ?>
                                                </span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">Wallet Information</h5>
                                        </div>
                                        <div class="card-body">
                                            <p><strong>Balance:</strong> <?php echo formatCurrency($walletData['balance']); ?></p>
                                            <p><strong>Total Earned:</strong> <?php echo formatCurrency($walletData['total_earned']); ?></p>
                                            <p><strong>Total Withdrawn:</strong> <?php echo formatCurrency($walletData['total_withdrawn']); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- PV Information -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">Own PV</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-6">
                                                    <div class="pv-display">
                                                        <div class="pv-value"><?php echo formatPV($pvTotals['left_pv']); ?></div>
                                                        <div>Left PV</div>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="pv-display">
                                                        <div class="pv-value"><?php echo formatPV($pvTotals['right_pv']); ?></div>
                                                        <div>Right PV</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">Downline PV</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-6">
                                                    <div class="pv-display">
                                                        <div class="pv-value"><?php echo formatPV($downlinePV['left_pv']); ?></div>
                                                        <div>Left Leg</div>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="pv-display">
                                                        <div class="pv-value"><?php echo formatPV($downlinePV['right_pv']); ?></div>
                                                        <div>Right Leg</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- PV Management Forms -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">Add PV</h5>
                                        </div>
                                        <div class="card-body">
                                            <form method="POST">
                                                <?php echo csrfTokenInput(); ?>
                                                <input type="hidden" name="action" value="add_pv">
                                                <input type="hidden" name="user_id" value="<?php echo $selectedUserId; ?>">
                                                
                                                <div class="mb-3">
                                                    <label for="pv_amount" class="form-label">PV Amount:</label>
                                                    <input type="number" class="form-control" id="pv_amount" name="pv_amount" 
                                                           step="0.01" min="0.01" required>
                                                </div>
                                                
                                                <div class="mb-3">
                                                    <label class="form-label">Side:</label>
                                                    <div class="row">
                                                        <div class="col-6">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="radio" name="side" id="left" value="left" required>
                                                                <label class="form-check-label" for="left">Left</label>
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="radio" name="side" id="right" value="right" required>
                                                                <label class="form-check-label" for="right">Right</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="mb-3">
                                                    <label for="description" class="form-label">Description:</label>
                                                    <input type="text" class="form-control" id="description" name="description" 
                                                           placeholder="Manual PV addition">
                                                </div>
                                                
                                                <button type="submit" class="btn btn-success">
                                                    <i class="fas fa-plus me-2"></i>Add PV
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">Manual Matching</h5>
                                        </div>
                                        <div class="card-body">
                                            <p class="text-muted">Run PV matching manually for this user.</p>
                                            <form method="POST">
                                                <?php echo csrfTokenInput(); ?>
                                                <input type="hidden" name="action" value="run_matching">
                                                <input type="hidden" name="user_id" value="<?php echo $selectedUserId; ?>">
                                                
                                                <button type="submit" class="btn btn-warning">
                                                    <i class="fas fa-sync me-2"></i>Run Matching
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
