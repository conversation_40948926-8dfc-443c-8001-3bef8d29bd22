<?php
/**
 * User Binary Tree Page
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/BinaryTree.php';
require_once '../includes/TreeVisualization.php';
require_once '../includes/PVSystem.php';

// Require user authentication
Auth::requireUser();

$currentUser = Auth::user();
$userId = Auth::id();

// Check if viewing another user's tree
$viewUserId = $_GET['view_user'] ?? $userId;

// Verify the user has permission to view this tree
$binaryTree = new BinaryTree();
if ($viewUserId !== $userId) {
    // Check if the view user is in current user's upline or downline
    $upline = $binaryTree->getUpline($userId);
    $downline = $binaryTree->getDownline($userId);

    if (!in_array($viewUserId, $upline) && !in_array($viewUserId, $downline) && $viewUserId !== $userId) {
        // User doesn't have permission to view this tree
        $viewUserId = $userId;
    }
}

// Initialize classes (already initialized above for permission check)
$pvSystem = new PVSystem();

// Get tree data for the user being viewed
$treeData = $binaryTree->getTreeStructure($viewUserId, 4); // Show 4 levels
$treeStats = $binaryTree->getTreeStats($viewUserId);

// Get PV data for the user being viewed
$ownPV = $pvSystem->getUserPVTotals($viewUserId);
$downlinePV = $pvSystem->getDownlinePVTotals($viewUserId);

// Get user details for the viewed user
$db = Database::getInstance();
$viewUserStmt = $db->prepare("SELECT user_id, full_name, email, status FROM users WHERE user_id = ?");
$viewUserStmt->execute([$viewUserId]);
$viewUserDetails = $viewUserStmt->fetch();

// Calculate totals
$totalLeftPV = $ownPV['left_pv'] + $downlinePV['left_pv'];
$totalRightPV = $ownPV['right_pv'] + $downlinePV['right_pv'];

// Get direct children info for the viewed user
$directChildren = $binaryTree->getDirectChildren($viewUserId);
$leftChild = null;
$rightChild = null;

if (!empty($directChildren['left'])) {
    $db = Database::getInstance();
    $stmt = $db->prepare("SELECT user_id, full_name, email, status FROM users WHERE user_id = ?");
    $stmt->execute([$directChildren['left']]);
    $leftChild = $stmt->fetch();
}

if (!empty($directChildren['right'])) {
    $stmt = $db->prepare("SELECT user_id, full_name, email, status FROM users WHERE user_id = ?");
    $stmt->execute([$directChildren['right']]);
    $rightChild = $stmt->fetch();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Binary Tree - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
            <img src="../assets/images/logo.png" alt="" style="height:50px; width:auto;"></i>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="tree.php">
                            <i class="fas fa-sitemap me-1"></i>Binary Tree
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="wallet.php">
                            <i class="fas fa-wallet me-1"></i>Wallet
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">
                            <i class="fas fa-shopping-cart me-1"></i>Products
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($currentUser['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Flash Messages -->
        <?php displayFlashMessages(); ?>
        
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="mb-0">
                                <i class="fas fa-sitemap me-2"></i>
                                <?php if ($viewUserId === $userId): ?>
                                    Your Binary Tree
                                <?php else: ?>
                                    <?php echo htmlspecialchars($viewUserDetails['full_name']); ?>'s Binary Tree
                                <?php endif; ?>
                            </h4>
                            <?php if ($viewUserId !== $userId): ?>
                                <a href="tree.php" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-home me-1"></i>Back to My Tree
                                </a>
                            <?php endif; ?>
                        </div>
                        <?php if ($viewUserId !== $userId): ?>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Viewing tree for User ID: <?php echo htmlspecialchars($viewUserId); ?>
                            </small>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tree Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon primary me-3">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>
                            <h5 class="mb-0"><?php echo $treeStats['total_downline']; ?></h5>
                            <small class="text-muted">Total Downline</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon success me-3">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                        <div>
                            <h5 class="mb-0"><?php echo $treeStats['left_leg_count']; ?></h5>
                            <small class="text-muted">Left Leg</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon warning me-3">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        <div>
                            <h5 class="mb-0"><?php echo $treeStats['right_leg_count']; ?></h5>
                            <small class="text-muted">Right Leg</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon info me-3">
                            <i class="fas fa-child"></i>
                        </div>
                        <div>
                            <h5 class="mb-0"><?php echo $treeStats['direct_children']; ?></h5>
                            <small class="text-muted">Direct Children</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- PV Summary -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Left Leg PV</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="pv-display mb-3" style="background: linear-gradient(135deg, #28a745, #20c997);">
                            <div class="pv-value"><?php echo formatPV($totalLeftPV); ?></div>
                            <div>Total Left PV</div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">Own PV</small><br>
                                <strong><?php echo formatPV($ownPV['left_pv']); ?></strong>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Downline PV</small><br>
                                <strong><?php echo formatPV($downlinePV['left_pv']); ?></strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Right Leg PV</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="pv-display mb-3" style="background: linear-gradient(135deg, #ffc107, #fd7e14);">
                            <div class="pv-value"><?php echo formatPV($totalRightPV); ?></div>
                            <div>Total Right PV</div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">Own PV</small><br>
                                <strong><?php echo formatPV($ownPV['right_pv']); ?></strong>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Downline PV</small><br>
                                <strong><?php echo formatPV($downlinePV['right_pv']); ?></strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Direct Children Info -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-arrow-left me-2"></i>Left Child</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($leftChild): ?>
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="fas fa-user-circle fa-3x text-success"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1"><?php echo htmlspecialchars($leftChild['full_name']); ?></h6>
                                    <p class="mb-1 text-muted"><?php echo htmlspecialchars($leftChild['user_id']); ?></p>
                                    <span class="status-badge status-<?php echo $leftChild['status']; ?>">
                                        <?php echo ucfirst($leftChild['status']); ?>
                                    </span>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="text-center text-muted">
                                <i class="fas fa-plus-circle fa-3x mb-3"></i>
                                <p>No left child yet</p>
                                <small>Share your referral link to add members</small>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-arrow-right me-2"></i>Right Child</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($rightChild): ?>
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="fas fa-user-circle fa-3x text-warning"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1"><?php echo htmlspecialchars($rightChild['full_name']); ?></h6>
                                    <p class="mb-1 text-muted"><?php echo htmlspecialchars($rightChild['user_id']); ?></p>
                                    <span class="status-badge status-<?php echo $rightChild['status']; ?>">
                                        <?php echo ucfirst($rightChild['status']); ?>
                                    </span>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="text-center text-muted">
                                <i class="fas fa-plus-circle fa-3x mb-3"></i>
                                <p>No right child yet</p>
                                <small>Share your referral link to add members</small>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tree Visualization -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-sitemap me-2"></i>Tree Structure (4 Levels)</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($treeData): ?>
                            <?php
                            // Use enhanced tree with hover functionality
                            echo TreeVisualization::getTreeCSS();
                            echo TreeVisualization::renderEnhancedTree($treeData, $userId);
                            echo TreeVisualization::getTreeJS();
                            ?>
                        <?php else: ?>
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-sitemap fa-4x mb-3"></i>
                                <h5>No tree data available</h5>
                                <p>Start building your network by sharing your referral link!</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
