<?php
/**
 * Database Migration: Add PV Usage Tracking Table
 * MLM Binary Plan System
 * 
 * This script adds the missing pv_usage_tracking table that is required
 * for the weekly PV matching system to work properly.
 */

require_once 'config/database.php';
require_once 'config/Connection.php';

echo "<!DOCTYPE html>\n";
echo "<html><head><title>PV Usage Tracking Migration</title></head><body>\n";
echo "<h1>🔧 PV Usage Tracking Table Migration</h1>\n";
echo "<p>This migration will add the missing <code>pv_usage_tracking</code> table required for weekly PV matching.</p>\n";

try {
    $db = Database::getInstance();
    
    echo "<h2>1. Checking Current Database State</h2>\n";
    
    // Check if table exists
    $tableCheck = $db->query("SHOW TABLES LIKE 'pv_usage_tracking'");
    $tableExists = $tableCheck->fetch() !== false;
    
    echo "<p>pv_usage_tracking table exists: " . ($tableExists ? '✅ Yes' : '❌ No') . "</p>\n";
    
    if ($tableExists) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
        echo "<h3>⚠️ Table Already Exists</h3>\n";
        echo "<p>The pv_usage_tracking table already exists. No migration needed.</p>\n";
        echo "</div>\n";
    } else {
        echo "<h2>2. Creating PV Usage Tracking Table</h2>\n";
        
        $createTableSQL = "
        CREATE TABLE IF NOT EXISTS pv_usage_tracking (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            pv_transaction_id INT NOT NULL,
            side ENUM('left', 'right') NOT NULL,
            original_amount DECIMAL(10,2) NOT NULL,
            used_amount DECIMAL(10,2) DEFAULT 0.00,
            remaining_amount DECIMAL(10,2) NOT NULL,
            week_used DATE NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIGN KEY (pv_transaction_id) REFERENCES pv_transactions(id) ON DELETE CASCADE,
            INDEX idx_user_remaining (user_id, remaining_amount),
            INDEX idx_side (side),
            INDEX idx_week_used (week_used),
            INDEX idx_created_at (created_at)
        )";
        
        try {
            $db->exec($createTableSQL);
            echo "<p style='color: green;'>✅ pv_usage_tracking table created successfully!</p>\n";
            
            echo "<h2>3. Initializing Existing PV Transactions</h2>\n";
            
            // Get all existing PV transactions that need to be tracked
            $existingPVStmt = $db->query("
                SELECT id, user_id, side, pv_amount, created_at 
                FROM pv_transactions 
                ORDER BY created_at ASC
            ");
            $existingPV = $existingPVStmt->fetchAll();
            
            echo "<p>Found " . count($existingPV) . " existing PV transactions to initialize.</p>\n";
            
            if (count($existingPV) > 0) {
                $insertStmt = $db->prepare("
                    INSERT INTO pv_usage_tracking 
                    (user_id, pv_transaction_id, side, original_amount, remaining_amount, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                
                $initialized = 0;
                foreach ($existingPV as $pv) {
                    try {
                        $insertStmt->execute([
                            $pv['user_id'],
                            $pv['id'],
                            $pv['side'],
                            $pv['pv_amount'],
                            $pv['pv_amount'], // Initially, remaining = original
                            $pv['created_at']
                        ]);
                        $initialized++;
                    } catch (Exception $e) {
                        echo "<p style='color: orange;'>⚠️ Warning: Could not initialize PV transaction ID {$pv['id']}: " . htmlspecialchars($e->getMessage()) . "</p>\n";
                    }
                }
                
                echo "<p style='color: green;'>✅ Initialized {$initialized} PV transactions in tracking table.</p>\n";
            }
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ Failed to create table: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            throw $e;
        }
    }
    
    echo "<h2>4. Verifying Table Structure</h2>\n";
    
    // Show table structure
    $structureStmt = $db->query("DESCRIBE pv_usage_tracking");
    $columns = $structureStmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
    echo "<tr style='background: #f8f9fa;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "<h2>5. Testing PV Usage Tracking</h2>\n";
    
    // Test basic functionality
    $testStmt = $db->query("SELECT COUNT(*) as count FROM pv_usage_tracking");
    $trackingCount = $testStmt->fetch()['count'];
    
    echo "<p>Total PV tracking records: {$trackingCount}</p>\n";
    
    if ($trackingCount > 0) {
        $sampleStmt = $db->query("
            SELECT user_id, side, original_amount, remaining_amount 
            FROM pv_usage_tracking 
            LIMIT 5
        ");
        $samples = $sampleStmt->fetchAll();
        
        echo "<h3>Sample Tracking Records:</h3>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
        echo "<tr style='background: #f8f9fa;'><th>User ID</th><th>Side</th><th>Original Amount</th><th>Remaining Amount</th></tr>\n";
        foreach ($samples as $sample) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($sample['user_id']) . "</td>";
            echo "<td>" . htmlspecialchars($sample['side']) . "</td>";
            echo "<td>" . number_format($sample['original_amount'], 2) . "</td>";
            echo "<td>" . number_format($sample['remaining_amount'], 2) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    echo "<h2>6. Summary</h2>\n";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h3>✅ Migration Completed Successfully!</h3>\n";
    echo "<p>The following changes have been made to fix the report generation issue:</p>\n";
    echo "<ul>\n";
    echo "<li>Created <code>pv_usage_tracking</code> table with proper structure and indexes</li>\n";
    echo "<li>Initialized tracking records for all existing PV transactions</li>\n";
    echo "<li>Set up foreign key relationships for data integrity</li>\n";
    echo "<li>Verified table functionality</li>\n";
    echo "</ul>\n";
    echo "<p><strong>The error 'Table pv_usage_tracking doesn't exist' should now be resolved!</strong></p>\n";
    echo "<p><strong>Weekly report generation should now work properly.</strong></p>\n";
    echo "</div>\n";
    
    echo "<h2>7. Next Steps</h2>\n";
    echo "<div style='background: #cce5ff; border: 1px solid #99ccff; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h3>📋 What to do next:</h3>\n";
    echo "<ol>\n";
    echo "<li>Test the weekly report generation in the admin panel</li>\n";
    echo "<li>Run the weekly matching cron job to verify functionality</li>\n";
    echo "<li>Monitor the system logs for any remaining issues</li>\n";
    echo "<li>Consider running the test script again to verify the fix</li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h3>❌ Migration Failed</h3>\n";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Please check your database connection and try again.</p>\n";
    echo "</div>\n";
}

echo "</body></html>\n";
?>
