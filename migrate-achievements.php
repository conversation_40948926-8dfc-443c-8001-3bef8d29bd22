<?php
/**
 * Database Migration for User Achievements
 * MLM Binary Plan System
 *
 * This script adds the new achievement columns to existing installations
 */

require_once 'config/database.php';

try {
    // Database configuration
    $host = DB_HOST;
    $dbname = DB_NAME;
    $username = DB_USER;
    $password = DB_PASS;
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];

    $pdo = new PDO($dsn, $username, $password, $options);
    echo "🔗 Connected to database successfully.\n";

    // Check if columns already exist
    $checkColumns = $pdo->query("SHOW COLUMNS FROM users LIKE 'user_level'");
    if ($checkColumns->rowCount() > 0) {
        echo "⚠️  Achievement columns already exist. Migration not needed.\n";
        exit;
    }

    echo "📝 Starting achievement migration...\n";

    // Add new columns to users table
    $alterUserTable = "
        ALTER TABLE users
        ADD COLUMN user_level ENUM('Beginner', 'Intermediate', 'Expert') DEFAULT 'Beginner' AFTER upline_pv,
        ADD COLUMN current_award VARCHAR(100) NULL AFTER user_level,
        ADD INDEX idx_user_level (user_level)
    ";

    $pdo->exec($alterUserTable);
    echo "✅ Added user_level and current_award columns to users table.\n";

    // Create user_achievements table
    $createAchievementsTable = "
        CREATE TABLE IF NOT EXISTS user_achievements (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            achievement_type ENUM('level', 'award') NOT NULL,
            previous_value VARCHAR(100) NULL,
            new_value VARCHAR(100) NOT NULL,
            assigned_by INT NOT NULL,
            admin_notes TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIGN KEY (assigned_by) REFERENCES admin(id),
            INDEX idx_user_id (user_id),
            INDEX idx_achievement_type (achievement_type),
            INDEX idx_created_at (created_at)
        )
    ";

    $pdo->exec($createAchievementsTable);
    echo "✅ Created user_achievements table.\n";

    // Set all existing users to Beginner level
    $updateExistingUsers = "UPDATE users SET user_level = 'Beginner' WHERE user_level IS NULL";
    $pdo->exec($updateExistingUsers);
    echo "✅ Set all existing users to Beginner level.\n";

    echo "🎉 Achievement migration completed successfully!\n";
    echo "\n📋 Summary:\n";
    echo "   - Added user_level column (Beginner, Intermediate, Expert)\n";
    echo "   - Added current_award column\n";
    echo "   - Created user_achievements history table\n";
    echo "   - Set all existing users to Beginner level\n";
    echo "\n🔧 Next steps:\n";
    echo "   - Access admin panel at /admin/user-achievements.php\n";
    echo "   - Start assigning levels and awards to users\n";
    echo "   - Users can view their achievements on dashboard and profile\n";

} catch (PDOException $e) {
    die("❌ Migration failed: " . $e->getMessage() . "\n");
}
?>
