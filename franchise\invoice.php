<?php
/**
 * Invoice Generation & Display
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';

// Require franchise authentication
if (!isLoggedIn('franchise')) {
    Response::redirect('login.php');
}

$currentUser = getCurrentUser();
$franchiseId = getCurrentUserId();

// Get order ID from URL
$orderId = $_GET['order_id'] ?? '';
$download = isset($_GET['download']);

if (!$orderId) {
    Response::redirect('billing.php');
}

// Get franchise details
$db = Database::getInstance();
$franchiseStmt = $db->prepare("SELECT * FROM franchise WHERE id = ?");
$franchiseStmt->execute([$franchiseId]);
$franchiseDetails = $franchiseStmt->fetch();

// Get order details
$orderStmt = $db->prepare("
    SELECT po.*, u.full_name, u.email, u.phone, u.address, u.user_id as user_code,
           p.name as product_name, p.description as product_description, p.product_code
    FROM purchase_orders po
    JOIN users u ON po.user_id = u.user_id
    JOIN products p ON po.product_id = p.id
    WHERE po.order_id = ? AND u.franchise_id = ?
");
$orderStmt->execute([$orderId, $franchiseId]);
$orderDetails = $orderStmt->fetch();

if (!$orderDetails) {
    Response::redirect('billing.php?error=' . urlencode('Order not found or access denied'));
}

// Calculate tax (assuming 18% GST)
$taxRate = 0.18;
$subtotal = $orderDetails['total_amount'];
$taxAmount = $subtotal * $taxRate;
$totalWithTax = $subtotal + $taxAmount;

// If download is requested, set headers for PDF download
if ($download) {
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="invoice-' . $orderId . '.pdf"');
    // Note: For actual PDF generation, you would use a library like TCPDF or mPDF
    // For now, we'll just show the HTML version
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice <?php echo htmlspecialchars($orderId); ?> - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            .container { max-width: none !important; }
        }
        .invoice-header {
            border-bottom: 3px solid #007bff;
            margin-bottom: 30px;
            padding-bottom: 20px;
        }
        .invoice-details {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .invoice-table th {
            background-color: #007bff;
            color: white;
        }
        .total-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- Action Buttons -->
        <div class="row no-print mb-3">
            <div class="col-12">
                <div class="d-flex justify-content-between">
                    <a href="billing.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Billing
                    </a>
                    <div>
                        <button onclick="window.print()" class="btn btn-primary me-2">
                            <i class="fas fa-print me-1"></i>Print Invoice
                        </button>
                        <a href="?order_id=<?php echo urlencode($orderId); ?>&download=1" class="btn btn-success">
                            <i class="fas fa-download me-1"></i>Download PDF
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invoice Content -->
        <div class="card">
            <div class="card-body">
                <!-- Invoice Header -->
                <div class="invoice-header">
                    <div class="row">
                        <div class="col-md-6">
                            <h1 class="text-primary">INVOICE</h1>
                            <h4><?php echo htmlspecialchars(SITE_NAME); ?></h4>
                            <p class="mb-0"Shaktipure Industries Pvt Ltd</p>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <h3>Invoice #<?php echo htmlspecialchars($orderId); ?></h3>
                            <p class="mb-1"><strong>Date:</strong> <?php echo date('d M Y', strtotime($orderDetails['created_at'])); ?></p>
                            <p class="mb-0"><strong>Status:</strong> 
                                <span class="badge bg-<?php echo $orderDetails['order_status'] === 'confirmed' ? 'success' : 'warning'; ?>">
                                    <?php echo ucfirst($orderDetails['order_status']); ?>
                                </span>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Franchise and Customer Details -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="invoice-details">
                            <h5 class="text-primary mb-3">From (Franchise)</h5>
                            <p class="mb-1"><strong><?php echo htmlspecialchars($franchiseDetails['full_name']); ?></strong></p>
                            <p class="mb-1">Franchise Code: <?php echo htmlspecialchars($franchiseDetails['franchise_code']); ?></p>
                            <p class="mb-1">Email: <?php echo htmlspecialchars($franchiseDetails['email']); ?></p>
                            <p class="mb-1">Phone: <?php echo htmlspecialchars($franchiseDetails['phone']); ?></p>
                            <?php if ($franchiseDetails['address']): ?>
                                <p class="mb-0">Address: <?php echo nl2br(htmlspecialchars($franchiseDetails['address'])); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="invoice-details">
                            <h5 class="text-primary mb-3">To (Customer)</h5>
                            <p class="mb-1"><strong><?php echo htmlspecialchars($orderDetails['full_name']); ?></strong></p>
                            <p class="mb-1">User ID: <?php echo htmlspecialchars($orderDetails['user_code']); ?></p>
                            <p class="mb-1">Email: <?php echo htmlspecialchars($orderDetails['email']); ?></p>
                            <p class="mb-1">Phone: <?php echo htmlspecialchars($orderDetails['phone']); ?></p>
                            <?php if ($orderDetails['address']): ?>
                                <p class="mb-0">Address: <?php echo nl2br(htmlspecialchars($orderDetails['address'])); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Order Details -->
                <div class="table-responsive mb-4">
                    <table class="table invoice-table">
                        <thead>
                            <tr>
                                <th>Product Code</th>
                                <th>Product Name</th>
                                <th>Description</th>
                                <th class="text-center">Quantity</th>
                                <th class="text-end">Unit Price</th>
                                <th class="text-end">PV Value</th>
                                <th class="text-end">Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><?php echo htmlspecialchars($orderDetails['product_code']); ?></td>
                                <td><strong><?php echo htmlspecialchars($orderDetails['product_name']); ?></strong></td>
                                <td><?php echo htmlspecialchars($orderDetails['product_description'] ?? 'N/A'); ?></td>
                                <td class="text-center"><?php echo number_format($orderDetails['quantity']); ?></td>
                                <td class="text-end"><?php echo formatCurrency($orderDetails['total_amount'] / $orderDetails['quantity']); ?></td>
                                <td class="text-end"><?php echo formatPV($orderDetails['pv_amount']); ?></td>
                                <td class="text-end"><strong><?php echo formatCurrency($orderDetails['total_amount']); ?></strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Totals -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="invoice-details">
                            <h6 class="text-primary mb-3">Payment Information</h6>
                            <p class="mb-1"><strong>Payment Method:</strong> <?php echo ucfirst($orderDetails['payment_method']); ?></p>
                            <p class="mb-1"><strong>Payment Status:</strong> 
                                <span class="badge bg-<?php echo $orderDetails['payment_status'] === 'completed' ? 'success' : 'warning'; ?>">
                                    <?php echo ucfirst($orderDetails['payment_status']); ?>
                                </span>
                            </p>
                            <p class="mb-1"><strong>PV Side:</strong> <?php echo ucfirst($orderDetails['placement_side']); ?></p>
                            <?php if ($orderDetails['payment_id']): ?>
                                <p class="mb-0"><strong>Payment ID:</strong> <?php echo htmlspecialchars($orderDetails['payment_id']); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="total-section">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Subtotal:</strong></td>
                                    <td class="text-end"><strong><?php echo formatCurrency($subtotal); ?></strong></td>
                                </tr>
                                <tr>
                                    <td>GST (18%):</td>
                                    <td class="text-end"><?php echo formatCurrency($taxAmount); ?></td>
                                </tr>
                                <tr class="border-top">
                                    <td><h5><strong>Total Amount:</strong></h5></td>
                                    <td class="text-end"><h5><strong><?php echo formatCurrency($totalWithTax); ?></strong></h5></td>
                                </tr>
                                <tr>
                                    <td><strong>Total PV:</strong></td>
                                    <td class="text-end"><strong><?php echo formatPV($orderDetails['pv_amount']); ?></strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="row mt-5">
                    <div class="col-12">
                        <div class="text-center border-top pt-3">
                            <p class="text-muted mb-1">Thank you for your business!</p>
                            <p class="text-muted mb-0">This is a computer-generated invoice and does not require a signature.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
