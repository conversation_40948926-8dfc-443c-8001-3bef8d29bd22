<?php
/**
 * Binary Tree Viewer (for testing)
 * MLM Binary Plan System
 */

require_once 'includes/header.php';
require_once 'includes/BinaryTree.php';
require_once 'includes/TreeVisualization.php';

// Get user ID from URL parameter
$userId = $_GET['user_id'] ?? null;

if (!$userId) {
    // Get first user from database for demo
    $db = Database::getInstance();
    $stmt = $db->prepare("SELECT user_id FROM users ORDER BY id LIMIT 1");
    $stmt->execute();
    $firstUser = $stmt->fetch();
    $userId = $firstUser['user_id'] ?? null;
}

$treeData = null;
$error = '';

if ($userId) {
    try {
        $binaryTree = new BinaryTree();
        $treeData = $binaryTree->getTreeStructure($userId, 4); // Show 4 levels
    } catch (Exception $e) {
        $error = 'Failed to load tree data: ' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Binary Tree Viewer - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
            <img src="../assets/images/logo.png" alt="" style="height:50px; width:auto;"></i>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-home me-2"></i>Home
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-sitemap me-2"></i>Binary Tree Viewer
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- User Selection Form -->
                        <form method="GET" class="mb-4">
                            <div class="row align-items-end">
                                <div class="col-md-6">
                                    <label for="user_id" class="form-label">Select User ID to view tree:</label>
                                    <input type="text" class="form-control" id="user_id" name="user_id" 
                                           value="<?php echo htmlspecialchars($userId ?? ''); ?>" 
                                           placeholder="Enter User ID">
                                </div>
                                <div class="col-md-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>View Tree
                                    </button>
                                </div>
                            </div>
                        </form>
                        
                        <?php if ($treeData): ?>
                            <!-- Tree Statistics -->
                            <?php
                            $binaryTree = new BinaryTree();
                            $stats = $binaryTree->getTreeStats($userId);
                            ?>
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="stats-card">
                                        <div class="d-flex align-items-center">
                                            <div class="stats-icon primary me-3">
                                                <i class="fas fa-users"></i>
                                            </div>
                                            <div>
                                                <h5 class="mb-0"><?php echo $stats['total_downline']; ?></h5>
                                                <small class="text-muted">Total Downline</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stats-card">
                                        <div class="d-flex align-items-center">
                                            <div class="stats-icon success me-3">
                                                <i class="fas fa-arrow-left"></i>
                                            </div>
                                            <div>
                                                <h5 class="mb-0"><?php echo $stats['left_leg_count']; ?></h5>
                                                <small class="text-muted">Left Leg</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stats-card">
                                        <div class="d-flex align-items-center">
                                            <div class="stats-icon warning me-3">
                                                <i class="fas fa-arrow-right"></i>
                                            </div>
                                            <div>
                                                <h5 class="mb-0"><?php echo $stats['right_leg_count']; ?></h5>
                                                <small class="text-muted">Right Leg</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stats-card">
                                        <div class="d-flex align-items-center">
                                            <div class="stats-icon info me-3">
                                                <i class="fas fa-child"></i>
                                            </div>
                                            <div>
                                                <h5 class="mb-0"><?php echo $stats['direct_children']; ?></h5>
                                                <small class="text-muted">Direct Children</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Tree Visualization -->
                            <div class="tree-section">
                                <h5 class="mb-3">
                                    <i class="fas fa-sitemap me-2"></i>Tree Structure for User: <?php echo htmlspecialchars($userId); ?>
                                </h5>
                                <?php echo TreeVisualization::renderCompleteTree($treeData, $userId); ?>
                            </div>
                            
                        <?php elseif ($userId): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                No tree data found for User ID: <?php echo htmlspecialchars($userId); ?>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Please enter a User ID to view the binary tree.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
