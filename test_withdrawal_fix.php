<?php
/**
 * Test script to verify withdrawal approval fix
 * This script tests the transaction management fix for withdrawal approvals
 */

require_once 'includes/Database.php';
require_once 'config/config.php';
require_once 'includes/Wallet.php';

echo "=== Testing Withdrawal Approval Fix ===\n\n";

try {
    $db = Database::getInstance();
    $wallet = new Wallet();

    // Test 1: Check if we can create a test user and wallet
    echo "Test 1: Creating test user and wallet...\n";

    $testUserId = 'TEST_' . time();

    // Create test user
    $userStmt = $db->prepare("INSERT INTO users (user_id, username, email, password, full_name, phone) VALUES (?, ?, ?, ?, ?, ?)");
    $userStmt->execute([
        $testUserId,
        'testuser_' . time(),
        'test_' . time() . '@example.com',
        password_hash('test123', PASSWORD_DEFAULT),
        'Test User',
        '**********'
    ]);

    // Create wallet and add some balance
    $wallet->createWallet($testUserId);
    $wallet->credit($testUserId, 1000.00, 'Test credit for withdrawal test');

    echo "✅ Test user created with ID: $testUserId\n";
    echo "✅ Wallet created with ₹1000 balance\n\n";

    // Test 2: Create a withdrawal request
    echo "Test 2: Creating withdrawal request...\n";

    $bankDetails = [
        'bank_name' => 'Test Bank',
        'account_number' => '**********',
        'ifsc_code' => 'TEST0001',
        'account_holder' => 'Test User'
    ];

    $wallet->requestWithdrawal($testUserId, 500.00, $bankDetails);

    // Get the withdrawal ID
    $withdrawalStmt = $db->prepare("SELECT id FROM withdrawals WHERE user_id = ? AND status = 'pending' ORDER BY id DESC LIMIT 1");
    $withdrawalStmt->execute([$testUserId]);
    $withdrawal = $withdrawalStmt->fetch();
    $withdrawalId = $withdrawal['id'];

    echo "✅ Withdrawal request created with ID: $withdrawalId\n\n";

    // Test 3: Create a test admin
    echo "Test 3: Creating test admin...\n";

    $adminStmt = $db->prepare("INSERT INTO admin (username, email, password, full_name) VALUES (?, ?, ?, ?)");
    $adminStmt->execute([
        'testadmin_' . time(),
        'admin_' . time() . '@example.com',
        password_hash('admin123', PASSWORD_DEFAULT),
        'Test Admin'
    ]);
    $adminId = $db->lastInsertId();

    echo "✅ Test admin created with ID: $adminId\n\n";

    // Test 4: Test withdrawal approval (this was failing before the fix)
    echo "Test 4: Testing withdrawal approval...\n";

    // Get balance before approval
    $walletBefore = $wallet->getWallet($testUserId);
    $balanceBefore = $walletBefore['balance'];
    echo "Balance before approval: ₹" . number_format($balanceBefore, 2) . "\n";

    // Approve the withdrawal
    $result = $wallet->processWithdrawal($withdrawalId, 'approved', $adminId, 'Test approval');

    if ($result) {
        echo "✅ Withdrawal approved successfully!\n";

        // Check balance after approval
        $walletAfter = $wallet->getWallet($testUserId);
        $balanceAfter = $walletAfter['balance'];
        echo "Balance after approval: ₹" . number_format($balanceAfter, 2) . "\n";

        // Verify the amount was deducted
        $expectedBalance = $balanceBefore - 500.00;
        if (abs($balanceAfter - $expectedBalance) < 0.01) {
            echo "✅ Correct amount deducted from wallet\n";
        } else {
            echo "❌ Incorrect balance after withdrawal\n";
        }

        // Check withdrawal status
        $withdrawalCheckStmt = $db->prepare("SELECT status, processed_by, admin_notes FROM withdrawals WHERE id = ?");
        $withdrawalCheckStmt->execute([$withdrawalId]);
        $withdrawalCheck = $withdrawalCheckStmt->fetch();

        if ($withdrawalCheck['status'] === 'approved' && $withdrawalCheck['processed_by'] == $adminId) {
            echo "✅ Withdrawal status updated correctly\n";
        } else {
            echo "❌ Withdrawal status not updated correctly\n";
        }

        // Check wallet transaction was created
        $transactionStmt = $db->prepare("SELECT * FROM wallet_transactions WHERE user_id = ? AND reference_type = 'withdrawal' AND reference_id = ?");
        $transactionStmt->execute([$testUserId, $withdrawalId]);
        $transaction = $transactionStmt->fetch();

        if ($transaction && $transaction['transaction_type'] === 'debit' && $transaction['amount'] == 500.00) {
            echo "✅ Wallet transaction recorded correctly\n";
        } else {
            echo "❌ Wallet transaction not recorded correctly\n";
        }

    } else {
        echo "❌ Withdrawal approval failed\n";
    }

    echo "\n";

    // Test 5: Test rejection
    echo "Test 5: Testing withdrawal rejection...\n";

    // Create another withdrawal request
    $wallet->requestWithdrawal($testUserId, 200.00, $bankDetails);

    $withdrawalStmt2 = $db->prepare("SELECT id FROM withdrawals WHERE user_id = ? AND status = 'pending' ORDER BY id DESC LIMIT 1");
    $withdrawalStmt2->execute([$testUserId]);
    $withdrawal2 = $withdrawalStmt2->fetch();
    $withdrawalId2 = $withdrawal2['id'];

    $balanceBeforeReject = $wallet->getBalance($testUserId);

    // Reject the withdrawal
    $rejectResult = $wallet->processWithdrawal($withdrawalId2, 'rejected', $adminId, 'Test rejection');

    if ($rejectResult) {
        echo "✅ Withdrawal rejected successfully!\n";

        $balanceAfterReject = $wallet->getBalance($testUserId);

        if (abs($balanceAfterReject - $balanceBeforeReject) < 0.01) {
            echo "✅ Balance unchanged after rejection (correct behavior)\n";
        } else {
            echo "❌ Balance changed after rejection (incorrect behavior)\n";
        }
    } else {
        echo "❌ Withdrawal rejection failed\n";
    }

    echo "\n";

    // Cleanup
    echo "Cleaning up test data...\n";
    $db->prepare("DELETE FROM wallet_transactions WHERE user_id = ?")->execute([$testUserId]);
    $db->prepare("DELETE FROM withdrawals WHERE user_id = ?")->execute([$testUserId]);
    $db->prepare("DELETE FROM wallet WHERE user_id = ?")->execute([$testUserId]);
    $db->prepare("DELETE FROM users WHERE user_id = ?")->execute([$testUserId]);
    $db->prepare("DELETE FROM admin WHERE id = ?")->execute([$adminId]);
    echo "✅ Test data cleaned up\n\n";

    echo "=== All Tests Completed Successfully! ===\n";
    echo "The withdrawal approval transaction issue has been fixed.\n";

} catch (Exception $e) {
    echo "❌ Test failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
