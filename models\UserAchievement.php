<?php
/**
 * User Achievement Model
 * MLM Binary Plan System
 */

require_once 'BaseModel.php';

class UserAchievement extends BaseModel {
    protected $table = 'user_achievements';
    protected $primaryKey = 'id';
    protected $fillable = [
        'user_id', 'achievement_type', 'previous_value', 'new_value', 
        'assigned_by', 'admin_notes'
    ];
    
    // Available levels
    const LEVELS = ['Beginner', 'Intermediate', 'Expert'];
    
    // Available awards
    const AWARDS = [
        'Top Performer',
        'Employee of the Month',
        'Outstanding Contributor',
        'Rising Star',
        'Team Leader',
        'Excellence Award',
        'Innovation Award',
        'Customer Champion'
    ];
    
    /**
     * Assign level to user
     * 
     * @param string $userId User ID
     * @param string $level New level
     * @param int $adminId Admin ID who assigned
     * @param string $notes Admin notes
     * @return bool Success or failure
     */
    public function assignLevel($userId, $level, $adminId, $notes = '') {
        if (!in_array($level, self::LEVELS)) {
            return false;
        }
        
        try {
            $this->db->beginTransaction();
            
            // Get current level
            $userStmt = $this->db->prepare("SELECT user_level FROM users WHERE user_id = ?");
            $userStmt->execute([$userId]);
            $currentUser = $userStmt->fetch();
            
            if (!$currentUser) {
                $this->db->rollback();
                return false;
            }
            
            $previousLevel = $currentUser['user_level'];
            
            // Update user level
            $updateStmt = $this->db->prepare("UPDATE users SET user_level = ? WHERE user_id = ?");
            $updateStmt->execute([$level, $userId]);
            
            // Record achievement history
            $historyStmt = $this->db->prepare("INSERT INTO user_achievements (user_id, achievement_type, previous_value, new_value, assigned_by, admin_notes) VALUES (?, 'level', ?, ?, ?, ?)");
            $historyStmt->execute([$userId, $previousLevel, $level, $adminId, $notes]);
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Error assigning level: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Assign award to user
     * 
     * @param string $userId User ID
     * @param string $award New award
     * @param int $adminId Admin ID who assigned
     * @param string $notes Admin notes
     * @return bool Success or failure
     */
    public function assignAward($userId, $award, $adminId, $notes = '') {
        if (!in_array($award, self::AWARDS)) {
            return false;
        }
        
        try {
            $this->db->beginTransaction();
            
            // Get current award
            $userStmt = $this->db->prepare("SELECT current_award FROM users WHERE user_id = ?");
            $userStmt->execute([$userId]);
            $currentUser = $userStmt->fetch();
            
            if (!$currentUser) {
                $this->db->rollback();
                return false;
            }
            
            $previousAward = $currentUser['current_award'];
            
            // Update user award
            $updateStmt = $this->db->prepare("UPDATE users SET current_award = ? WHERE user_id = ?");
            $updateStmt->execute([$award, $userId]);
            
            // Record achievement history
            $historyStmt = $this->db->prepare("INSERT INTO user_achievements (user_id, achievement_type, previous_value, new_value, assigned_by, admin_notes) VALUES (?, 'award', ?, ?, ?, ?)");
            $historyStmt->execute([$userId, $previousAward, $award, $adminId, $notes]);
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Error assigning award: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Remove award from user
     * 
     * @param string $userId User ID
     * @param int $adminId Admin ID who removed
     * @param string $notes Admin notes
     * @return bool Success or failure
     */
    public function removeAward($userId, $adminId, $notes = '') {
        try {
            $this->db->beginTransaction();
            
            // Get current award
            $userStmt = $this->db->prepare("SELECT current_award FROM users WHERE user_id = ?");
            $userStmt->execute([$userId]);
            $currentUser = $userStmt->fetch();
            
            if (!$currentUser) {
                $this->db->rollback();
                return false;
            }
            
            $previousAward = $currentUser['current_award'];
            
            // Remove user award
            $updateStmt = $this->db->prepare("UPDATE users SET current_award = NULL WHERE user_id = ?");
            $updateStmt->execute([$userId]);
            
            // Record achievement history
            $historyStmt = $this->db->prepare("INSERT INTO user_achievements (user_id, achievement_type, previous_value, new_value, assigned_by, admin_notes) VALUES (?, 'award', ?, 'Removed', ?, ?)");
            $historyStmt->execute([$userId, $previousAward, $adminId, $notes]);
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Error removing award: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get user achievement history
     * 
     * @param string $userId User ID
     * @param int $limit Number of records to fetch
     * @return array Achievement history
     */
    public function getUserAchievementHistory($userId, $limit = 10) {
        $stmt = $this->db->prepare("
            SELECT ua.*, a.full_name as admin_name 
            FROM user_achievements ua 
            JOIN admin a ON ua.assigned_by = a.id 
            WHERE ua.user_id = ? 
            ORDER BY ua.created_at DESC 
            LIMIT ?
        ");
        $stmt->execute([$userId, $limit]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get achievement statistics
     * 
     * @return array Statistics
     */
    public function getAchievementStats() {
        $stats = [];
        
        // Level distribution
        $levelStmt = $this->db->query("
            SELECT user_level, COUNT(*) as count 
            FROM users 
            WHERE status = 'active' 
            GROUP BY user_level
        ");
        $stats['levels'] = $levelStmt->fetchAll();
        
        // Award distribution
        $awardStmt = $this->db->query("
            SELECT current_award, COUNT(*) as count 
            FROM users 
            WHERE status = 'active' AND current_award IS NOT NULL 
            GROUP BY current_award
        ");
        $stats['awards'] = $awardStmt->fetchAll();
        
        // Recent achievements
        $recentStmt = $this->db->query("
            SELECT ua.*, u.full_name as user_name, a.full_name as admin_name 
            FROM user_achievements ua 
            JOIN users u ON ua.user_id = u.user_id 
            JOIN admin a ON ua.assigned_by = a.id 
            ORDER BY ua.created_at DESC 
            LIMIT 10
        ");
        $stats['recent'] = $recentStmt->fetchAll();
        
        return $stats;
    }
    
    /**
     * Get users by level
     * 
     * @param string $level Level to filter by
     * @return array Users with specified level
     */
    public function getUsersByLevel($level) {
        if (!in_array($level, self::LEVELS)) {
            return [];
        }
        
        $stmt = $this->db->prepare("
            SELECT user_id, full_name, email, current_award, registration_date 
            FROM users 
            WHERE user_level = ? AND status = 'active' 
            ORDER BY full_name
        ");
        $stmt->execute([$level]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get users by award
     * 
     * @param string $award Award to filter by
     * @return array Users with specified award
     */
    public function getUsersByAward($award) {
        if (!in_array($award, self::AWARDS)) {
            return [];
        }
        
        $stmt = $this->db->prepare("
            SELECT user_id, full_name, email, user_level, registration_date 
            FROM users 
            WHERE current_award = ? AND status = 'active' 
            ORDER BY full_name
        ");
        $stmt->execute([$award]);
        return $stmt->fetchAll();
    }
}
