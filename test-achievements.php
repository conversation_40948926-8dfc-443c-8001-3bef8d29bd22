<?php
/**
 * Test script for User Achievement System
 * MLM Binary Plan System
 */

require_once 'includes/header.php';
require_once 'models/UserAchievement.php';

echo "🧪 Testing User Achievement System\n\n";

try {
    $userAchievement = new UserAchievement();
    
    // Test 1: Check available levels and awards
    echo "📋 Available Levels: " . implode(', ', UserAchievement::LEVELS) . "\n";
    echo "🏆 Available Awards: " . implode(', ', UserAchievement::AWARDS) . "\n\n";
    
    // Test 2: Get achievement statistics
    echo "📊 Getting achievement statistics...\n";
    $stats = $userAchievement->getAchievementStats();
    
    echo "Level Distribution:\n";
    foreach ($stats['levels'] as $level) {
        echo "  - {$level['user_level']}: {$level['count']} users\n";
    }
    
    echo "\nAward Distribution:\n";
    if (empty($stats['awards'])) {
        echo "  - No awards assigned yet\n";
    } else {
        foreach ($stats['awards'] as $award) {
            echo "  - {$award['current_award']}: {$award['count']} users\n";
        }
    }
    
    echo "\nRecent Achievement Changes:\n";
    if (empty($stats['recent'])) {
        echo "  - No recent changes\n";
    } else {
        foreach ($stats['recent'] as $recent) {
            echo "  - {$recent['user_name']}: {$recent['achievement_type']} changed to {$recent['new_value']}\n";
        }
    }
    
    // Test 3: Get users by level
    echo "\n👥 Users by level:\n";
    foreach (UserAchievement::LEVELS as $level) {
        $users = $userAchievement->getUsersByLevel($level);
        echo "  - {$level}: " . count($users) . " users\n";
    }
    
    echo "\n✅ All tests completed successfully!\n";
    echo "\n🎯 System is ready for use:\n";
    echo "   - Admin can assign levels and awards at /admin/user-achievements.php\n";
    echo "   - Users can view achievements on dashboard and profile\n";
    echo "   - All changes are logged in user_achievements table\n";
    
} catch (Exception $e) {
    echo "❌ Test failed: " . $e->getMessage() . "\n";
}
?>
