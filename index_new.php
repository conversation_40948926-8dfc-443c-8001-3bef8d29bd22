<?php
/**
 * Modern E-commerce Homepage
 * Celio-style Design with Dynamic Data
 */

// Enable error reporting in development
if (!defined('ENVIRONMENT') || ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// Include essential files
require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/session.php';
require_once 'includes/functions.php';
require_once 'includes/FileUpload.php';

// Check if user is already logged in and redirect accordingly
if (isLoggedIn()) {
    $userType = getCurrentUserType();
    switch ($userType) {
        case 'admin':
            header("Location: admin/dashboard.php");
            break;
        case 'franchise':
            header("Location: franchise/dashboard.php");
            break;
        case 'user':
            header("Location: user/dashboard.php");
            break;
        default:
            destroyUserSession();
            break;
    }
    exit();
}

// Get database instance
$db = Database::getInstance();
$fileUpload = new FileUpload();

// Fetch dynamic data
try {
    // Get hero banners
    $bannersStmt = $db->prepare("SELECT * FROM banners WHERE status = 'active' AND position = 'hero' ORDER BY sort_order ASC LIMIT 3");
    $bannersStmt->execute();
    $heroBanners = $bannersStmt->fetchAll();

    // Get featured collections
    $collectionsStmt = $db->prepare("SELECT * FROM collections WHERE status = 'active' AND is_featured = 1 ORDER BY sort_order ASC LIMIT 8");
    $collectionsStmt->execute();
    $featuredCollections = $collectionsStmt->fetchAll();

    // Get featured products
    $featuredStmt = $db->prepare("
        SELECT p.*, pc.name as category_name 
        FROM products p 
        LEFT JOIN product_categories pc ON p.category_id = pc.id 
        WHERE p.status = 'active' AND p.is_featured = 1 
        ORDER BY p.created_at DESC 
        LIMIT 5
    ");
    $featuredStmt->execute();
    $featuredProducts = $featuredStmt->fetchAll();

    // Get deal of the day products
    $dealsStmt = $db->prepare("
        SELECT p.*, pc.name as category_name, d.title as deal_title, d.end_date as deal_end_date
        FROM products p 
        LEFT JOIN product_categories pc ON p.category_id = pc.id 
        LEFT JOIN deals d ON JSON_CONTAINS(d.product_ids, CAST(p.id AS JSON))
        WHERE p.status = 'active' AND p.is_deal_of_day = 1 
        AND d.status = 'active' AND d.end_date > NOW()
        ORDER BY p.created_at DESC 
        LIMIT 4
    ");
    $dealsStmt->execute();
    $dealProducts = $dealsStmt->fetchAll();

    // Get categories for navigation
    $categoriesStmt = $db->prepare("SELECT * FROM product_categories WHERE status = 'active' ORDER BY sort_order ASC");
    $categoriesStmt->execute();
    $categories = $categoriesStmt->fetchAll();

} catch (Exception $e) {
    // Fallback to empty arrays if database queries fail
    $heroBanners = [];
    $featuredCollections = [];
    $featuredProducts = [];
    $dealProducts = [];
    $categories = [];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Celio - Stay Ahead With Latest Gadgets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1e40af;
            --primary-orange: #f97316;
            --dark-blue: #1e3a8a;
            --light-blue: #dbeafe;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --white: #ffffff;
            --light-gray: #f8fafc;
            --border-color: #e5e7eb;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            background-color: var(--white);
        }

        /* Top Header */
        .top-header {
            background: var(--primary-blue);
            color: white;
            padding: 8px 0;
            font-size: 13px;
        }

        .top-header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .top-header-left {
            font-weight: 500;
        }

        .top-header-right {
            display: flex;
            gap: 20px;
        }

        .top-header-right a {
            color: white;
            text-decoration: none;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        /* Main Header */
        .main-header {
            background: white;
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
            font-size: 28px;
            font-weight: 800;
            color: var(--primary-blue);
        }

        .search-bar {
            flex: 1;
            max-width: 500px;
            margin: 0 40px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 12px 50px 12px 20px;
            border: 2px solid var(--border-color);
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            border-color: var(--primary-blue);
        }

        .search-btn {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--primary-orange);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .cart-btn {
            background: var(--primary-orange);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        /* Navigation */
        .navigation {
            background: var(--primary-blue);
            padding: 0;
        }

        .nav-content {
            display: flex;
            align-items: center;
        }

        .categories-btn {
            background: var(--dark-blue);
            color: white;
            padding: 15px 25px;
            border: none;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
            cursor: pointer;
            font-size: 14px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            flex: 1;
        }

        .nav-menu li {
            margin: 0;
        }

        .nav-menu a {
            color: white;
            text-decoration: none;
            padding: 15px 20px;
            display: block;
            font-weight: 500;
            font-size: 14px;
            transition: background 0.3s ease;
        }

        .nav-menu a:hover {
            background: rgba(255,255,255,0.1);
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            padding: 60px 0;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .hero-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-height: 400px;
        }

        .hero-text {
            flex: 1;
            padding-right: 40px;
        }

        .hero-subtitle {
            font-size: 18px;
            margin-bottom: 15px;
            color: #93c5fd;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 20px;
            line-height: 1.1;
        }

        .hero-description {
            font-size: 18px;
            margin-bottom: 30px;
            color: #dbeafe;
            line-height: 1.6;
        }

        .hero-buttons {
            display: flex;
            gap: 15px;
        }

        .btn-primary-hero {
            background: var(--primary-orange);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.3s ease;
        }

        .btn-primary-hero:hover {
            transform: translateY(-2px);
            color: white;
        }

        .btn-secondary-hero {
            background: transparent;
            color: white;
            padding: 15px 30px;
            border: 2px solid white;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .btn-secondary-hero:hover {
            background: white;
            color: var(--primary-blue);
        }

        .hero-image {
            flex: 1;
            text-align: center;
        }

        .hero-image img {
            max-width: 100%;
            height: auto;
            border-radius: 15px;
        }

        /* Collections Section */
        .collections-section {
            padding: 60px 0;
            background: var(--light-gray);
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 50px;
            color: var(--text-dark);
        }

        .collections-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .collection-item {
            background: white;
            padding: 25px 15px;
            border-radius: 12px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid var(--border-color);
        }

        .collection-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .collection-icon {
            font-size: 2.5rem;
            color: var(--primary-blue);
            margin-bottom: 15px;
        }

        .collection-name {
            font-weight: 600;
            font-size: 14px;
            color: var(--text-dark);
        }

        .collection-count {
            font-size: 12px;
            color: var(--text-light);
            margin-top: 5px;
        }

        /* Featured Products */
        .featured-section {
            padding: 60px 0;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
        }

        .product-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            position: relative;
            border: 1px solid var(--border-color);
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .product-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            background: #ef4444;
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            z-index: 2;
        }

        .product-image {
            height: 220px;
            background: #f8fafc;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .product-image img {
            max-width: 100%;
            max-height: 100%;
            object-fit: cover;
        }

        .product-info {
            padding: 20px;
        }

        .product-brand {
            font-size: 12px;
            color: var(--text-light);
            margin-bottom: 5px;
            text-transform: uppercase;
            font-weight: 500;
        }

        .product-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 10px;
            line-height: 1.3;
        }

        .product-rating {
            display: flex;
            align-items: center;
            gap: 5px;
            margin-bottom: 10px;
        }

        .stars {
            color: #fbbf24;
            font-size: 14px;
        }

        .rating-count {
            font-size: 12px;
            color: var(--text-light);
        }

        .product-price {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .current-price {
            font-size: 18px;
            font-weight: 700;
            color: var(--primary-blue);
        }

        .original-price {
            font-size: 14px;
            color: var(--text-light);
            text-decoration: line-through;
        }

        .add-to-cart {
            width: 100%;
            background: var(--primary-orange);
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .add-to-cart:hover {
            background: #ea580c;
        }

        /* Deal of the Day */
        .deals-section {
            padding: 60px 0;
            background: var(--light-gray);
        }

        .deals-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
        }

        .countdown-timer {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .timer-item {
            background: var(--primary-blue);
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            text-align: center;
            min-width: 60px;
        }

        .timer-number {
            font-size: 20px;
            font-weight: 700;
            display: block;
        }

        .timer-label {
            font-size: 10px;
            text-transform: uppercase;
        }

        /* Promotional Banners */
        .promo-section {
            padding: 60px 0;
        }

        .promo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .promo-banner {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            border-radius: 15px;
            padding: 40px;
            color: white;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .promo-banner.orange {
            background: linear-gradient(135deg, #f97316 0%, #fb923c 100%);
        }

        .promo-banner.green {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        }

        .promo-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .promo-subtitle {
            font-size: 16px;
            margin-bottom: 25px;
            opacity: 0.9;
        }

        .promo-btn {
            background: white;
            color: var(--primary-blue);
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
        }

        /* Footer */
        .footer {
            background: #1f2937;
            color: white;
            padding: 50px 0 20px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 40px;
        }

        .footer-section h5 {
            font-weight: 600;
            margin-bottom: 20px;
            color: white;
        }

        .footer-section ul {
            list-style: none;
            padding: 0;
        }

        .footer-section ul li {
            margin-bottom: 10px;
        }

        .footer-section ul li a {
            color: #9ca3af;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .footer-section ul li a:hover {
            color: white;
        }

        .footer-bottom {
            border-top: 1px solid #374151;
            padding-top: 20px;
            text-align: center;
            color: #9ca3af;
            font-size: 14px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-content {
                flex-direction: column;
                text-align: center;
            }
            
            .hero-text {
                padding-right: 0;
                margin-bottom: 30px;
            }
            
            .collections-grid {
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 15px;
            }
            
            .products-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
            }
            
            .search-bar {
                margin: 15px 0;
                max-width: 100%;
            }
            
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
        }

        @media (max-width: 576px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .section-title {
                font-size: 2rem;
            }
            
            .nav-menu {
                flex-direction: column;
                width: 100%;
            }
            
            .nav-content {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Top Header -->
    <div class="top-header">
        <div class="container">
            <div class="top-header-left">
                Free Shipping Available Over ₹500 | Easy Returns Within 30 Days
            </div>
            <div class="top-header-right">
                <a href="user/register.php"><i class="fas fa-user"></i> Register</a>
                <a href="user/login.php"><i class="fas fa-sign-in-alt"></i> Sign In</a>
                <a href="#"><i class="fas fa-heart"></i> Wishlist</a>
                <a href="#"><i class="fas fa-phone"></i> Help</a>
            </div>
        </div>
    </div>

    <!-- Main Header -->
    <header class="main-header">
        <div class="container">
            <div class="header-content">
                <!-- Logo -->
                <a href="index.php" class="logo">
                    Celio
                </a>

                <!-- Search Bar -->
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="Search for products, brands and more...">
                    <button class="search-btn"><i class="fas fa-search"></i></button>
                </div>

                <!-- Header Actions -->
                <div class="header-actions">
                    <a href="#" class="cart-btn">
                        <i class="fas fa-shopping-cart"></i>
                        Cart (0)
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="navigation">
        <div class="container">
            <div class="nav-content">
                <button class="categories-btn">
                    <i class="fas fa-bars"></i>
                    All Categories
                </button>
                <ul class="nav-menu">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="#">Electronics</a></li>
                    <li><a href="#">Offers</a></li>
                    <li><a href="#">About Us</a></li>
                    <li><a href="#">Contact</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <div class="hero-subtitle">Discover Innovation</div>
                    <h1 class="hero-title">Stay Ahead With Latest Gadgets</h1>
                    <p class="hero-description">
                        Discover the newest innovations that keep you ahead. Experience cutting-edge technology 
                        with our premium collection of gadgets and electronics.
                    </p>
                    <div class="hero-buttons">
                        <a href="#featured" class="btn-primary-hero">Shop Now</a>
                        <a href="#collections" class="btn-secondary-hero">See More</a>
                    </div>
                </div>
                <div class="hero-image">
                    <img src="assets/images/hero-headphones.png" alt="Latest Gadgets" style="max-width: 400px;">
                </div>
            </div>
        </div>
    </section>

    <!-- Collections Section -->
    <section class="collections-section" id="collections">
        <div class="container">
            <h2 class="section-title">Collection</h2>
            <div class="collections-grid">
                <?php if (!empty($featuredCollections)): ?>
                    <?php foreach ($featuredCollections as $collection): ?>
                        <div class="collection-item" onclick="location.href='products.php?collection=<?php echo urlencode($collection['slug']); ?>'">
                            <div class="collection-icon">
                                <i class="<?php echo htmlspecialchars($collection['icon']); ?>"></i>
                            </div>
                            <div class="collection-name"><?php echo htmlspecialchars($collection['name']); ?></div>
                            <div class="collection-count">5 items</div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- Default collections if no data -->
                    <div class="collection-item">
                        <div class="collection-icon"><i class="fas fa-headphones"></i></div>
                        <div class="collection-name">Earbuds</div>
                        <div class="collection-count">5 items</div>
                    </div>
                    <div class="collection-item">
                        <div class="collection-icon"><i class="fas fa-hdd"></i></div>
                        <div class="collection-name">Hard Drives</div>
                        <div class="collection-count">3 items</div>
                    </div>
                    <div class="collection-item">
                        <div class="collection-icon"><i class="fas fa-keyboard"></i></div>
                        <div class="collection-name">Keyboards</div>
                        <div class="collection-count">8 items</div>
                    </div>
                    <div class="collection-item">
                        <div class="collection-icon"><i class="fas fa-mobile-alt"></i></div>
                        <div class="collection-name">Mobile</div>
                        <div class="collection-count">12 items</div>
                    </div>
                    <div class="collection-item">
                        <div class="collection-icon"><i class="fas fa-print"></i></div>
                        <div class="collection-name">Printers</div>
                        <div class="collection-count">4 items</div>
                    </div>
                    <div class="collection-item">
                        <div class="collection-icon"><i class="fas fa-laptop"></i></div>
                        <div class="collection-name">Laptops</div>
                        <div class="collection-count">6 items</div>
                    </div>
                    <div class="collection-item">
                        <div class="collection-icon"><i class="fas fa-tablet-alt"></i></div>
                        <div class="collection-name">Tablets</div>
                        <div class="collection-count">7 items</div>
                    </div>
                    <div class="collection-item">
                        <div class="collection-icon"><i class="fas fa-headphones"></i></div>
                        <div class="collection-name">Headphones</div>
                        <div class="collection-count">9 items</div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Featured Collection -->
    <section class="featured-section" id="featured">
        <div class="container">
            <h2 class="section-title">Featured Collection</h2>
            <div class="products-grid">
                <?php if (!empty($featuredProducts)): ?>
                    <?php foreach ($featuredProducts as $product): ?>
                        <div class="product-card">
                            <?php if ($product['discount_percentage'] > 0): ?>
                                <div class="product-badge">-<?php echo $product['discount_percentage']; ?>%</div>
                            <?php endif; ?>
                            
                            <div class="product-image">
                                <?php if ($product['image']): ?>
                                    <img src="<?php echo htmlspecialchars($product['image']); ?>" 
                                         alt="<?php echo htmlspecialchars($product['name']); ?>">
                                <?php else: ?>
                                    <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #dbeafe, #bfdbfe); display: flex; align-items: center; justify-content: center; color: #1e40af; font-size: 24px; font-weight: bold;">
                                        <?php echo strtoupper(substr($product['name'], 0, 2)); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="product-info">
                                <div class="product-brand"><?php echo htmlspecialchars($product['brand'] ?? 'Celio'); ?></div>
                                <div class="product-title"><?php echo htmlspecialchars($product['name']); ?></div>
                                
                                <div class="product-rating">
                                    <div class="stars">
                                        <?php 
                                        $rating = $product['rating'] ?? 4.5;
                                        for ($i = 1; $i <= 5; $i++): 
                                        ?>
                                            <i class="fas fa-star<?php echo $i <= $rating ? '' : '-o'; ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <span class="rating-count">(<?php echo $product['review_count'] ?? 0; ?>)</span>
                                </div>
                                
                                <div class="product-price">
                                    <span class="current-price">₹<?php echo number_format($product['price'], 0); ?></span>
                                    <?php if ($product['original_price'] && $product['original_price'] > $product['price']): ?>
                                        <span class="original-price">₹<?php echo number_format($product['original_price'], 0); ?></span>
                                    <?php endif; ?>
                                </div>
                                
                                <button class="add-to-cart">Add to Cart</button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- Default products if no data -->
                    <div class="product-card">
                        <div class="product-badge">-33%</div>
                        <div class="product-image">
                            <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #dbeafe, #bfdbfe); display: flex; align-items: center; justify-content: center; color: #1e40af; font-size: 24px; font-weight: bold;">PB</div>
                        </div>
                        <div class="product-info">
                            <div class="product-brand">PowerTech</div>
                            <div class="product-title">Compact Pocket Size Power Bank</div>
                            <div class="product-rating">
                                <div class="stars">★★★★☆</div>
                                <span class="rating-count">(125)</span>
                            </div>
                            <div class="product-price">
                                <span class="current-price">₹1,400</span>
                                <span class="original-price">₹2,100</span>
                            </div>
                            <button class="add-to-cart">Add to Cart</button>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Deal of the Day -->
    <section class="deals-section">
        <div class="container">
            <div class="deals-header">
                <h2 class="section-title" style="margin-bottom: 0;">Deal Of The Day</h2>
                <div class="countdown-timer">
                    <div class="timer-item">
                        <span class="timer-number" id="days">23</span>
                        <span class="timer-label">Days</span>
                    </div>
                    <div class="timer-item">
                        <span class="timer-number" id="hours">04</span>
                        <span class="timer-label">Hours</span>
                    </div>
                    <div class="timer-item">
                        <span class="timer-number" id="minutes">19</span>
                        <span class="timer-label">Minutes</span>
                    </div>
                    <div class="timer-item">
                        <span class="timer-number" id="seconds">26</span>
                        <span class="timer-label">Seconds</span>
                    </div>
                </div>
            </div>
            
            <div class="products-grid">
                <?php if (!empty($dealProducts)): ?>
                    <?php foreach ($dealProducts as $product): ?>
                        <div class="product-card">
                            <div class="product-badge">Deal Lock</div>
                            <div class="product-image">
                                <?php if ($product['image']): ?>
                                    <img src="<?php echo htmlspecialchars($product['image']); ?>" 
                                         alt="<?php echo htmlspecialchars($product['name']); ?>">
                                <?php else: ?>
                                    <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #fef3c7, #fde68a); display: flex; align-items: center; justify-content: center; color: #d97706; font-size: 24px; font-weight: bold;">
                                        <?php echo strtoupper(substr($product['name'], 0, 2)); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="product-info">
                                <div class="product-brand"><?php echo htmlspecialchars($product['brand'] ?? 'Celio'); ?></div>
                                <div class="product-title"><?php echo htmlspecialchars($product['name']); ?></div>
                                <div class="product-rating">
                                    <div class="stars">
                                        <?php 
                                        $rating = $product['rating'] ?? 4.5;
                                        for ($i = 1; $i <= 5; $i++): 
                                        ?>
                                            <i class="fas fa-star<?php echo $i <= $rating ? '' : '-o'; ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <span class="rating-count">(<?php echo $product['review_count'] ?? 0; ?>)</span>
                                </div>
                                <div class="product-price">
                                    <span class="current-price">₹<?php echo number_format($product['price'], 0); ?></span>
                                    <?php if ($product['original_price'] && $product['original_price'] > $product['price']): ?>
                                        <span class="original-price">₹<?php echo number_format($product['original_price'], 0); ?></span>
                                    <?php endif; ?>
                                </div>
                                <button class="add-to-cart">Add to Cart</button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- Default deal products -->
                    <div class="product-card">
                        <div class="product-badge">Deal Lock</div>
                        <div class="product-image">
                            <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #fef3c7, #fde68a); display: flex; align-items: center; justify-content: center; color: #d97706; font-size: 24px; font-weight: bold;">PJ</div>
                        </div>
                        <div class="product-info">
                            <div class="product-brand">ProjectMax</div>
                            <div class="product-title">Wireless Digital Multimedia Projector</div>
                            <div class="product-rating">
                                <div class="stars">★★★★★</div>
                                <span class="rating-count">(67)</span>
                            </div>
                            <div class="product-price">
                                <span class="current-price">₹9,900</span>
                                <span class="original-price">₹11,785</span>
                            </div>
                            <button class="add-to-cart">Add to Cart</button>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Promotional Banners -->
    <section class="promo-section">
        <div class="container">
            <div class="promo-grid">
                <div class="promo-banner">
                    <h3 class="promo-title">Smartphones</h3>
                    <p class="promo-subtitle">Innovation</p>
                    <a href="#" class="promo-btn">Shop Now</a>
                </div>
                <div class="promo-banner orange">
                    <h3 class="promo-title">Audio Freedom</h3>
                    <p class="promo-subtitle">Sound Experience</p>
                    <a href="#" class="promo-btn">Shop Now</a>
                </div>
                <div class="promo-banner green">
                    <h3 class="promo-title">Power of Portable</h3>
                    <p class="promo-subtitle">Tablets</p>
                    <a href="#" class="promo-btn">Shop Now</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h5>My Account</h5>
                    <ul>
                        <li><a href="#">My Account</a></li>
                        <li><a href="user/register.php">Register</a></li>
                        <li><a href="user/login.php">Login</a></li>
                        <li><a href="#">Order History</a></li>
                        <li><a href="#">Wishlist</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h5>Customer Service</h5>
                    <ul>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">Returns</a></li>
                        <li><a href="#">Site Map</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms & Conditions</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h5>Information</h5>
                    <ul>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Delivery Information</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms & Conditions</a></li>
                        <li><a href="#">Contact Us</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h5>Follow Us</h5>
                    <ul>
                        <li><a href="#"><i class="fab fa-facebook"></i> Facebook</a></li>
                        <li><a href="#"><i class="fab fa-twitter"></i> Twitter</a></li>
                        <li><a href="#"><i class="fab fa-instagram"></i> Instagram</a></li>
                        <li><a href="#"><i class="fab fa-youtube"></i> YouTube</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Celio. All rights reserved. | Powered by ShaktiPure Industries</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Countdown Timer
        function updateCountdown() {
            const now = new Date().getTime();
            const endTime = now + (23 * 24 * 60 * 60 * 1000) + (4 * 60 * 60 * 1000) + (19 * 60 * 1000) + (26 * 1000);
            
            const distance = endTime - now;
            
            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);
            
            document.getElementById('days').textContent = days.toString().padStart(2, '0');
            document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
            document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
            document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
        }
        
        // Update countdown every second
        setInterval(updateCountdown, 1000);
        updateCountdown();

        // Search functionality
        document.querySelector('.search-btn').addEventListener('click', function() {
            const searchInput = document.querySelector('.search-input');
            const searchTerm = searchInput.value.trim();
            if (searchTerm) {
                window.location.href = `products.php?search=${encodeURIComponent(searchTerm)}`;
            }
        });

        // Enter key search
        document.querySelector('.search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.querySelector('.search-btn').click();
            }
        });

        // Add to cart functionality
        document.querySelectorAll('.add-to-cart').forEach(button => {
            button.addEventListener('click', function() {
                // Add animation
                this.innerHTML = '<i class="fas fa-check"></i> Added!';
                this.style.background = '#10b981';
                
                setTimeout(() => {
                    this.innerHTML = 'Add to Cart';
                    this.style.background = '';
                }, 2000);
            });
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Categories dropdown (placeholder)
        document.querySelector('.categories-btn').addEventListener('click', function() {
            alert('Categories dropdown coming soon!');
        });
    </script>
</body>
</html>
