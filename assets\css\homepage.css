/**
 * Homepage Styles - Modern E-commerce Design
 * Responsive design inspired by modern tech e-commerce sites
 */

:root {
    /* Color Palette - Light Theme */
    --primary-light: #ffffff;
    --primary-blue: #2563eb;
    --primary-purple: #8b5cf6;
    --accent-orange: #f97316;
    --accent-cyan: #0891b2;
    --text-dark: #1f2937;
    --text-gray-light: #6b7280;
    --text-gray-dark: #374151;
    --background-light: #f8fafc;
    --background-card: #ffffff;
    --border-color: #e5e7eb;

    /* Gradients - Light Theme */
    --gradient-primary: linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%);
    --gradient-hero: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
    --gradient-card: linear-gradient(145deg, #ffffff 0%, #f1f5f9 100%);

    /* Spacing */
    --container-max-width: 1400px;
    --section-padding: 80px 0;
    --card-border-radius: 16px;
    --button-border-radius: 12px;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--background-light);
    color: var(--text-dark);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.top-header {
    background: var(--primary-blue);
    border-bottom: 1px solid var(--border-color);
    padding: 12px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.top-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.top-header-left {
    font-size: 14px;
    color: white;
}

.top-header-right {
    display: flex;
    gap: 20px;
    align-items: center;
}

.top-header-right a {
    color: white;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.top-header-right a:hover {
    color: rgba(255, 255, 255, 0.8);
}

/* Main Header */
.main-header {
    background: var(--primary-light);
    padding: 20px 0;
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 40px;
}

.logo {
    font-size: 28px;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-decoration: none;
}

.logo1{
    height: 50px;
    width: auto;
}

.search-bar {
    flex: 1;
    max-width: 500px;
    position: relative;
}

.search-bar form {
    position: relative;
    width: 100%;
}

.search-bar input {
    width: 100%;
    padding: 14px 50px 14px 20px;
    border: 2px solid var(--border-color);
    border-radius: var(--button-border-radius);
    background: var(--background-card);
    color: var(--text-dark);
    font-size: 16px;
    transition: all 0.3s ease;
}

.search-bar input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.search-bar input::placeholder {
    color: var(--text-gray-light);
}

.search-bar button {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--gradient-primary);
    border: none;
    padding: 10px 15px;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-bar button:hover {
    transform: translateY(-50%) scale(1.05);
}

.header-actions {
    display: flex;
    gap: 20px;
    align-items: center;
}

.header-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--text-gray-light);
    transition: all 0.3s ease;
    padding: 10px;
    border-radius: 8px;
}

.header-btn:hover {
    color: var(--text-dark);
    background: rgba(0, 0, 0, 0.05);
}

.header-btn i {
    font-size: 20px;
    margin-bottom: 4px;
}

.header-btn span {
    font-size: 12px;
    font-weight: 500;
}

/* Navigation */
.navigation {
    background: var(--text-dark);
    padding: 0;
    border-bottom: 1px solid var(--border-color);
}

.nav-content {
    display: flex;
    align-items: center;
    gap: 40px;
}

.categories-btn {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 16px 24px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
}

.categories-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
    margin: 0;
    padding: 0;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    padding: 16px 0;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: rgba(255, 255, 255, 0.8);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

/* Hero Section */
.hero-section {
    background: var(--gradient-hero);
    padding: var(--section-padding);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-text h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 24px;
    background: linear-gradient(135deg, #ffffff 0%, #cbd5e1 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    color: var(--accent-cyan);
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-description {
    font-size: 18px;
    color: var(--text-gray-light);
    margin-bottom: 40px;
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.btn-primary-hero {
    background: var(--gradient-primary);
    color: white;
    padding: 16px 32px;
    border-radius: var(--button-border-radius);
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary-hero:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(30, 64, 175, 0.4);
    color: white;
}

.btn-secondary-hero {
    background: transparent;
    color: var(--text-white);
    padding: 16px 32px;
    border: 2px solid var(--border-color);
    border-radius: var(--button-border-radius);
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
}

.btn-secondary-hero:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--text-white);
    color: var(--text-white);
    transform: translateY(-3px);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.3));
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Categories Section */
.categories-section {
    padding: var(--section-padding);
    background: var(--background-light);
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    margin-bottom: 16px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 18px;
    color: var(--text-gray-light);
    max-width: 600px;
    margin: 0 auto;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.category-card {
    background: var(--gradient-card);
    border-radius: var(--card-border-radius);
    padding: 40px 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.category-card:hover::before {
    left: 100%;
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-color: var(--primary-blue);
}

.category-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: white;
}

.category-card h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-dark);
}

.category-card p {
    color: var(--text-gray-light);
    font-size: 14px;
}

/* Products Section */
.products-section {
    padding: var(--section-padding);
    background: var(--primary-light);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    min-height: 200px; /* Ensure minimum height */
}

/* Ensure products display properly */
.products-grid .product-card {
    display: block;
    opacity: 1;
    visibility: visible;
}

.product-card {
    background: var(--gradient-card);
    border-radius: var(--card-border-radius);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    position: relative;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.product-image {
    position: relative;
    overflow: hidden;
    height: 250px;
    background: var(--background-card);
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--accent-orange);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.product-info {
    padding: 24px;
}

.product-code {
    font-size: 12px;
    color: var(--accent-cyan);
    font-weight: 600;
    text-transform: uppercase;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
}

.product-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-dark);
}

.product-description {
    color: var(--text-gray-light);
    font-size: 14px;
    margin-bottom: 16px;
    line-height: 1.5;
}

.product-price {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.current-price {
    font-size: 20px;
    font-weight: 700;
    color: var(--accent-cyan);
}

.pv-value {
    font-size: 14px;
    color: var(--accent-orange);
    font-weight: 600;
    background: rgba(249, 115, 22, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
}

.original-price {
    font-size: 16px;
    color: var(--text-gray-light);
    text-decoration: line-through;
}

.product-actions {
    display: flex;
    gap: 10px;
}

.btn-add-cart {
    flex: 1;
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: var(--button-border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-add-cart:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(30, 64, 175, 0.3);
}

.btn-wishlist {
    background: transparent;
    border: 2px solid var(--border-color);
    color: var(--text-gray-light);
    padding: 12px;
    border-radius: var(--button-border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-wishlist:hover {
    border-color: var(--accent-orange);
    color: var(--accent-orange);
}

/* Footer */
.footer {
    background: var(--text-dark);
    border-top: 1px solid var(--border-color);
    padding: 60px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: white;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: white;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--background-card);
    border-radius: 50%;
    color: var(--text-gray-light);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--gradient-primary);
    color: white;
    transform: translateY(-3px);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 30px;
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .container {
        padding: 0 15px;
    }

    .hero-content {
        gap: 40px;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 20px;
    }

    .search-bar {
        order: 3;
        max-width: 100%;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-buttons {
        justify-content: center;
    }

    .nav-content {
        flex-direction: column;
        gap: 0;
    }

    .categories-btn {
        width: 100%;
        justify-content: center;
    }

    .nav-menu {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
        gap: 15px;
        padding: 15px 0;
    }

    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 20px;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn-primary-hero,
    .btn-secondary-hero {
        width: 100%;
        max-width: 300px;
        text-align: center;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }

    .hero-section {
        padding: 40px 0;
    }

    .categories-section,
    .products-section {
        padding: 40px 0;
    }

    .category-card {
        padding: 30px 15px;
    }

    .category-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }

    .product-info {
        padding: 20px;
    }

    .header-actions {
        gap: 10px;
    }

    .header-btn {
        padding: 8px;
    }

    .header-btn i {
        font-size: 18px;
    }

    .header-btn span {
        font-size: 11px;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--text-white);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }
.mb-5 { margin-bottom: 40px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }
.mt-5 { margin-top: 40px; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

.w-full { width: 100%; }
.h-full { height: 100%; }

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus Styles for Accessibility */
button:focus,
input:focus,
a:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .top-header,
    .navigation,
    .hero-section,
    .footer {
        display: none;
    }

    body {
        background: white;
        color: black;
    }
}
