<?php
/**
 * Custom Error Page
 * MLM Binary Plan System
 */

// Get error code
$errorCode = isset($_GET['code']) ? (int) $_GET['code'] : 404;

// Define error messages
$errorMessages = [
    400 => 'Bad Request',
    401 => 'Unauthorized',
    403 => 'Forbidden',
    404 => 'Page Not Found',
    500 => 'Internal Server Error',
    503 => 'Service Unavailable'
];

// Set default message if code not found
$errorMessage = $errorMessages[$errorCode] ?? 'Unknown Error';

// Set HTTP response code
http_response_code($errorCode);

// Simple error logging
error_log("HTTP Error {$errorCode}: {$errorMessage} - URL: " . ($_SERVER['REQUEST_URI'] ?? 'unknown'));

// Get friendly error description
$errorDescription = '';
switch ($errorCode) {
    case 400:
        $errorDescription = 'The request could not be understood by the server due to malformed syntax.';
        break;
    case 401:
        $errorDescription = 'You need to be logged in to access this page.';
        break;
    case 403:
        $errorDescription = 'You do not have permission to access this page.';
        break;
    case 404:
        $errorDescription = 'The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.';
        break;
    case 500:
        $errorDescription = 'The server encountered an unexpected condition which prevented it from fulfilling the request.';
        break;
    case 503:
        $errorDescription = 'The server is currently unavailable. Please try again later.';
        break;
    default:
        $errorDescription = 'An unexpected error occurred.';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error <?php echo $errorCode; ?> - ShaktiPure Industries Pvt Ltd</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .error-container {
            max-width: 600px;
            margin: 100px auto;
            padding: 40px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        .error-code {
            font-size: 72px;
            font-weight: 700;
            color: #dc3545;
            margin-bottom: 20px;
        }
        .error-message {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #343a40;
        }
        .error-description {
            color: #6c757d;
            margin-bottom: 30px;
        }
        .error-icon {
            font-size: 60px;
            color: #dc3545;
            margin-bottom: 20px;
        }
        .btn-home {
            padding: 10px 30px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-container">
            <?php if ($errorCode === 404): ?>
                <div class="error-icon"><i class="fas fa-search"></i></div>
            <?php elseif ($errorCode === 403): ?>
                <div class="error-icon"><i class="fas fa-lock"></i></div>
            <?php elseif ($errorCode === 401): ?>
                <div class="error-icon"><i class="fas fa-user-lock"></i></div>
            <?php elseif ($errorCode === 500): ?>
                <div class="error-icon"><i class="fas fa-exclamation-triangle"></i></div>
            <?php else: ?>
                <div class="error-icon"><i class="fas fa-exclamation-circle"></i></div>
            <?php endif; ?>
            
            <div class="error-code"><?php echo $errorCode; ?></div>
            <div class="error-message"><?php echo $errorMessage; ?></div>
            <div class="error-description"><?php echo $errorDescription; ?></div>
            
            <div class="mt-4">
                <a href="/" class="btn btn-primary btn-home">
                    <i class="fas fa-home me-2"></i> Back to Home
                </a>
            </div>
            
            <?php if ($errorCode === 404): ?>
            <div class="mt-4">
                <p class="text-muted">If you believe this is an error, please contact our support team.</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
