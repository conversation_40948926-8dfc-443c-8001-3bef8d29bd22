<?php
/**
 * AJAX endpoint for getting user achievement history
 * MLM Binary Plan System
 */

require_once '../../includes/header.php';
require_once '../../includes/Auth.php';
require_once '../../models/UserAchievement.php';

// Require admin authentication
if (!Auth::check('admin')) {
    http_response_code(403);
    echo '<div class="alert alert-danger">Access denied</div>';
    exit;
}

$userId = $_GET['user_id'] ?? '';

if (empty($userId)) {
    echo '<div class="alert alert-warning">Invalid user ID</div>';
    exit;
}

$userAchievement = new UserAchievement();
$history = $userAchievement->getUserAchievementHistory($userId, 20);

if (empty($history)): ?>
    <div class="text-center py-4">
        <i class="fas fa-history fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">No Achievement History</h5>
        <p class="text-muted">This user has no recorded achievement changes.</p>
    </div>
<?php else: ?>
    <div class="timeline">
        <?php foreach ($history as $record): ?>
            <div class="timeline-item">
                <div class="timeline-marker">
                    <?php if ($record['achievement_type'] === 'level'): ?>
                        <i class="fas fa-level-up-alt text-primary"></i>
                    <?php elseif ($record['new_value'] === 'Removed'): ?>
                        <i class="fas fa-times text-danger"></i>
                    <?php else: ?>
                        <i class="fas fa-trophy text-warning"></i>
                    <?php endif; ?>
                </div>
                <div class="timeline-content">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="mb-0">
                            <?php if ($record['achievement_type'] === 'level'): ?>
                                Level Changed
                            <?php elseif ($record['new_value'] === 'Removed'): ?>
                                Award Removed
                            <?php else: ?>
                                Award Assigned
                            <?php endif; ?>
                        </h6>
                        <small class="text-muted"><?php echo date('M d, Y H:i', strtotime($record['created_at'])); ?></small>
                    </div>
                    
                    <div class="mb-2">
                        <?php if ($record['achievement_type'] === 'level'): ?>
                            <span class="badge bg-secondary"><?php echo htmlspecialchars($record['previous_value']); ?></span>
                            <i class="fas fa-arrow-right mx-2"></i>
                            <span class="badge bg-primary"><?php echo htmlspecialchars($record['new_value']); ?></span>
                        <?php elseif ($record['new_value'] === 'Removed'): ?>
                            <span class="badge bg-warning"><?php echo htmlspecialchars($record['previous_value']); ?></span>
                            <i class="fas fa-arrow-right mx-2"></i>
                            <span class="badge bg-danger">Removed</span>
                        <?php else: ?>
                            <?php if ($record['previous_value']): ?>
                                <span class="badge bg-secondary"><?php echo htmlspecialchars($record['previous_value']); ?></span>
                                <i class="fas fa-arrow-right mx-2"></i>
                            <?php endif; ?>
                            <span class="badge bg-warning"><?php echo htmlspecialchars($record['new_value']); ?></span>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($record['admin_notes']): ?>
                        <div class="alert alert-light py-2 mb-2">
                            <small><strong>Notes:</strong> <?php echo htmlspecialchars($record['admin_notes']); ?></small>
                        </div>
                    <?php endif; ?>
                    
                    <small class="text-muted">
                        <i class="fas fa-user-shield me-1"></i>
                        Assigned by: <?php echo htmlspecialchars($record['admin_name']); ?>
                    </small>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>
