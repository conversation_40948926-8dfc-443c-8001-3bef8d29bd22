<?php
/**
 * PV (Point Value) System Class
 * MLM Binary Plan System
 * Handles PV transactions, matching, and income calculation
 */

require_once __DIR__ . '/BinaryTree.php';
require_once __DIR__ . '/../config/config.php';

class PVSystem {
    private $db;
    private $config;

    public function __construct() {
        $this->db = Database::getInstance();
        $this->config = Config::getInstance();
    }

    /**
     * Add PV to user's account
     */
    public function addPV($userId, $pvAmount, $side, $transactionType = 'purchase', $productId = null, $referenceId = null, $description = '', $createdByType = 'system', $createdById = null) {
        try {
            $this->db->beginTransaction();

            // Validate required parameters
            if (empty($userId) || empty($pvAmount) || empty($side)) {
                throw new Exception("Missing required parameters: userId, pvAmount, or side");
            }

            // Validate side parameter
            if (!in_array($side, ['left', 'right'])) {
                throw new Exception("Invalid side parameter. Must be 'left' or 'right'");
            }

            // Insert PV transaction
            $stmt = $this->db->prepare("INSERT INTO pv_transactions (user_id, transaction_type, pv_amount, side, product_id, reference_id, description, created_by_type, created_by_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $result = $stmt->execute([
                $userId,
                $transactionType,
                $pvAmount,
                $side,
                $productId,
                $referenceId,
                $description,
                $createdByType,
                $createdById
            ]);

            if (!$result) {
                throw new Exception("Failed to insert PV transaction");
            }

            // Skip PV matching for now to isolate the issue
            // TODO: Re-enable PV matching once basic PV addition is working
            /*
            try {
                $this->triggerPVMatching($userId);
            } catch (Exception $matchingError) {
                error_log("PV Matching error (non-critical): " . $matchingError->getMessage());
                // Continue without failing the main transaction
            }
            */

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            if ($this->db->inTransaction()) {
                $this->db->rollback();
            }
            error_log("Add PV error: " . $e->getMessage());
            error_log("Add PV error details - userId: $userId, pvAmount: $pvAmount, side: $side, transactionType: $transactionType");
            return false;
        }
    }

    /**
     * Add PV without matching (simplified version for debugging)
     */
    public function addPVSimple($userId, $pvAmount, $side, $transactionType = 'purchase', $productId = null, $referenceId = null, $description = '', $createdByType = 'system', $createdById = null) {
        try {
            // Validate required parameters
            if (empty($userId) || empty($pvAmount) || empty($side)) {
                throw new Exception("Missing required parameters: userId, pvAmount, or side");
            }

            // Validate side parameter
            if (!in_array($side, ['left', 'right'])) {
                throw new Exception("Invalid side parameter. Must be 'left' or 'right'");
            }

            // Insert PV transaction without transaction wrapper
            $stmt = $this->db->prepare("INSERT INTO pv_transactions (user_id, transaction_type, pv_amount, side, product_id, reference_id, description, created_by_type, created_by_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $result = $stmt->execute([
                $userId,
                $transactionType,
                $pvAmount,
                $side,
                $productId,
                $referenceId,
                $description,
                $createdByType,
                $createdById
            ]);

            if (!$result) {
                throw new Exception("Failed to insert PV transaction");
            }

            return true;

        } catch (Exception $e) {
            error_log("Add PV Simple error: " . $e->getMessage());
            error_log("Add PV Simple error details - userId: $userId, pvAmount: $pvAmount, side: $side, transactionType: $transactionType");
            return false;
        }
    }

    /**
     * Add PV to user's self PV and propagate to upline
     */
    public function addSelfPV($userId, $pvAmount, $transactionType = 'purchase', $productId = null, $referenceId = null, $description = '', $createdByType = 'system', $createdById = null) {
        try {
            // Validate required parameters
            if (empty($userId) || empty($pvAmount)) {
                throw new Exception("Missing required parameters: userId or pvAmount");
            }

            $this->db->beginTransaction();

            // 1. Add PV transaction record with 'self' side
            $stmt = $this->db->prepare("INSERT INTO pv_transactions (user_id, transaction_type, pv_amount, side, product_id, reference_id, description, created_by_type, created_by_id) VALUES (?, ?, ?, 'self', ?, ?, ?, ?, ?)");
            $result = $stmt->execute([
                $userId,
                $transactionType,
                $pvAmount,
                $productId,
                $referenceId,
                $description,
                $createdByType,
                $createdById
            ]);

            if (!$result) {
                throw new Exception("Failed to insert PV transaction");
            }

            // 2. Update user's self_pv in users table
            $updateSelfStmt = $this->db->prepare("UPDATE users SET self_pv = self_pv + ? WHERE user_id = ?");
            $updateSelfResult = $updateSelfStmt->execute([$pvAmount, $userId]);

            if (!$updateSelfResult) {
                throw new Exception("Failed to update user's self PV");
            }

            // 3. Propagate PV to upline sponsors
            $this->propagatePVToUpline($userId, $pvAmount);

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            if ($this->db->inTransaction()) {
                $this->db->rollback();
            }
            error_log("Add Self PV error: " . $e->getMessage());
            error_log("Add Self PV error details - userId: $userId, pvAmount: $pvAmount, transactionType: $transactionType");
            return false;
        }
    }

    /**
     * Propagate PV to upline sponsors based on binary tree placement
     */
    private function propagatePVToUpline($userId, $pvAmount) {
        try {
            // Include BinaryTree class
            if (!class_exists('BinaryTree')) {
                require_once 'BinaryTree.php';
            }

            $binaryTree = new BinaryTree();
            $currentUserId = $userId;

            // Traverse up the binary tree and add PV to sponsors based on placement side
            while (true) {
                // Get current user's binary tree node
                $currentNode = $binaryTree->getNode($currentUserId);
                if (!$currentNode || !$currentNode['parent_id']) {
                    break; // Reached the top of the tree
                }

                $parentId = $currentNode['parent_id'];
                $placementSide = $currentNode['position']; // 'left' or 'right'

                // Add PV to parent's left or right PV based on placement side
                if ($placementSide === 'left') {
                    // Add to parent's left PV directly (avoid recursion)
                    $this->addDirectPV($parentId, $pvAmount, 'left', 'downline_bonus', null, "DOWNLINE_FROM_{$currentUserId}", "Left PV from downline user {$currentUserId}");
                } elseif ($placementSide === 'right') {
                    // Add to parent's right PV directly (avoid recursion)
                    $this->addDirectPV($parentId, $pvAmount, 'right', 'downline_bonus', null, "DOWNLINE_FROM_{$currentUserId}", "Right PV from downline user {$currentUserId}");
                }

                // Move up to the next level
                $currentUserId = $parentId;
            }

        } catch (Exception $e) {
            error_log("PV Propagation error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Add PV directly without triggering propagation (to avoid recursion)
     */
    public function addDirectPV($userId, $pvAmount, $side, $transactionType = 'downline_bonus', $productId = null, $referenceId = null, $description = '') {
        try {
            // Insert PV transaction record
            $stmt = $this->db->prepare("INSERT INTO pv_transactions (user_id, transaction_type, pv_amount, side, product_id, reference_id, description, created_by_type, created_by_id) VALUES (?, ?, ?, ?, ?, ?, ?, 'system', NULL)");
            $result = $stmt->execute([
                $userId,
                $transactionType,
                $pvAmount,
                $side,
                $productId,
                $referenceId,
                $description
            ]);

            if (!$result) {
                throw new Exception("Failed to insert direct PV transaction for user {$userId}");
            }

            return true;

        } catch (Exception $e) {
            error_log("Add Direct PV error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get sponsor chain for a user (all upline sponsors)
     */
    private function getSponsorChain($userId, $maxLevels = 10) {
        $sponsors = [];
        $currentUserId = $userId;
        $level = 0;

        while ($level < $maxLevels) {
            // Get the sponsor of the current user
            $sponsorStmt = $this->db->prepare("SELECT sponsor_id FROM users WHERE user_id = ? AND sponsor_id IS NOT NULL");
            $sponsorStmt->execute([$currentUserId]);
            $sponsor = $sponsorStmt->fetch();

            if (!$sponsor || !$sponsor['sponsor_id']) {
                break; // No more sponsors in the chain
            }

            $sponsors[] = $sponsor['sponsor_id'];
            $currentUserId = $sponsor['sponsor_id'];
            $level++;
        }

        return $sponsors;
    }

    /**
     * Get user's PV totals
     */
    public function getUserPVTotals($userId) {
        // Get PV from transactions table
        $stmt = $this->db->prepare("
            SELECT
                SUM(CASE WHEN side = 'left' THEN pv_amount ELSE 0 END) as left_pv,
                SUM(CASE WHEN side = 'right' THEN pv_amount ELSE 0 END) as right_pv,
                SUM(CASE WHEN side = 'self' THEN pv_amount ELSE 0 END) as self_pv,
                SUM(CASE WHEN side = 'upline' THEN pv_amount ELSE 0 END) as upline_pv,
                SUM(pv_amount) as total_pv
            FROM pv_transactions
            WHERE user_id = ?
        ");
        $stmt->execute([$userId]);
        $result = $stmt->fetch();

        // Also get current balances from users table
        $userStmt = $this->db->prepare("SELECT self_pv, upline_pv FROM users WHERE user_id = ?");
        $userStmt->execute([$userId]);
        $userResult = $userStmt->fetch();

        return [
            'left_pv' => (float) ($result['left_pv'] ?? 0),
            'right_pv' => (float) ($result['right_pv'] ?? 0),
            'self_pv' => (float) ($userResult['self_pv'] ?? 0),
            'upline_pv' => (float) ($userResult['upline_pv'] ?? 0),
            'total_pv' => (float) ($result['total_pv'] ?? 0)
        ];
    }

    /**
     * Get user's downline PV totals
     */
    public function getDownlinePVTotals($userId) {
        $binaryTree = new BinaryTree();
        $leftLeg = $binaryTree->getLeftLeg($userId);
        $rightLeg = $binaryTree->getRightLeg($userId);

        $leftPV = 0;
        $rightPV = 0;

        // Calculate left leg PV
        if (!empty($leftLeg)) {
            $leftUserIds = "'" . implode("','", $leftLeg) . "'";
            $leftStmt = $this->db->query("SELECT SUM(pv_amount) as total FROM pv_transactions WHERE user_id IN ($leftUserIds)");
            $leftResult = $leftStmt->fetch();
            $leftPV = (float) ($leftResult['total'] ?? 0);
        }

        // Calculate right leg PV
        if (!empty($rightLeg)) {
            $rightUserIds = "'" . implode("','", $rightLeg) . "'";
            $rightStmt = $this->db->query("SELECT SUM(pv_amount) as total FROM pv_transactions WHERE user_id IN ($rightUserIds)");
            $rightResult = $rightStmt->fetch();
            $rightPV = (float) ($rightResult['total'] ?? 0);
        }

        return [
            'left_pv' => $leftPV,
            'right_pv' => $rightPV,
            'total_pv' => $leftPV + $rightPV
        ];
    }

    /**
     * Trigger PV matching for user's upline
     */
    public function triggerPVMatching($userId) {
        try {
            // Check if BinaryTree class exists
            if (!class_exists('BinaryTree')) {
                throw new Exception("BinaryTree class not found");
            }

            $binaryTree = new BinaryTree();
            $upline = $binaryTree->getUpline($userId);

            // Process matching for each upline member
            if (is_array($upline)) {
                foreach ($upline as $uplineUserId) {
                    try {
                        $this->processPVMatching($uplineUserId);
                    } catch (Exception $e) {
                        error_log("PV Matching error for user $uplineUserId: " . $e->getMessage());
                        // Continue with other upline members
                    }
                }
            }
        } catch (Exception $e) {
            error_log("Trigger PV Matching error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Process PV matching for a specific user
     */
    public function processPVMatching($userId) {
        try {
            // Get current PV totals including downline
            $ownPV = $this->getUserPVTotals($userId);
            $downlinePV = $this->getDownlinePVTotals($userId);

            $totalLeftPV = $ownPV['left_pv'] + $downlinePV['left_pv'];
            $totalRightPV = $ownPV['right_pv'] + $downlinePV['right_pv'];

            // Get previous matching data
            $lastMatching = $this->getLastMatching($userId);
            $carriedLeftPV = $lastMatching['carry_forward_left'] ?? 0;
            $carriedRightPV = $lastMatching['carry_forward_right'] ?? 0;

            // Add carried forward PV
            $totalLeftPV += $carriedLeftPV;
            $totalRightPV += $carriedRightPV;

            // Calculate matching PV (minimum of left and right)
            $matchedPV = min($totalLeftPV, $totalRightPV);

            if ($matchedPV > 0) {
                // Calculate income (1 PV = configured rate)
                $pvRate = $this->config->getPVRate();
                $incomeAmount = $matchedPV * $pvRate;

                // Apply daily capping
                $dailyCapping = $this->config->getDailyCapping();
                $todayIncome = $this->getTodayIncome($userId);
                $availableCapping = $dailyCapping - $todayIncome;

                $cappingApplied = 0;
                if ($incomeAmount > $availableCapping) {
                    $cappingApplied = $incomeAmount - $availableCapping;
                    $incomeAmount = $availableCapping;
                }

                // Calculate carry forward
                $carryForwardLeft = $totalLeftPV - $matchedPV;
                $carryForwardRight = $totalRightPV - $matchedPV;

                // Record income log
                $this->recordIncomeLog($userId, $totalLeftPV, $totalRightPV, $matchedPV, $incomeAmount, $cappingApplied, $carryForwardLeft, $carryForwardRight);

                // Credit income to wallet
                if ($incomeAmount > 0) {
                    $this->creditWallet($userId, $incomeAmount, 'PV Matching Income');
                }
            }

        } catch (Exception $e) {
            error_log("PV matching error for user $userId: " . $e->getMessage());
        }
    }

    /**
     * Get last matching record for user
     */
    public function getLastMatching($userId) {
        $stmt = $this->db->prepare("SELECT * FROM income_logs WHERE user_id = ? ORDER BY matching_date DESC LIMIT 1");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    }

    /**
     * Get today's income for user
     */
    public function getTodayIncome($userId) {
        $today = date('Y-m-d');
        $stmt = $this->db->prepare("SELECT SUM(income_amount) as total FROM income_logs WHERE user_id = ? AND matching_date = ?");
        $stmt->execute([$userId, $today]);
        $result = $stmt->fetch();
        return (float) ($result['total'] ?? 0);
    }

    /**
     * Record income log
     */
    public function recordIncomeLog($userId, $leftPV, $rightPV, $matchedPV, $incomeAmount, $cappingApplied, $carryForwardLeft, $carryForwardRight) {
        $stmt = $this->db->prepare("INSERT INTO income_logs (user_id, left_pv, right_pv, matched_pv, income_amount, capping_applied, carry_forward_left, carry_forward_right, matching_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $userId,
            $leftPV,
            $rightPV,
            $matchedPV,
            $incomeAmount,
            $cappingApplied,
            $carryForwardLeft,
            $carryForwardRight,
            date('Y-m-d')
        ]);
    }

    /**
     * Credit amount to user's wallet
     */
    public function creditWallet($userId, $amount, $description) {
        try {
            // Use the Wallet class for consistent wallet management
            $wallet = new Wallet();
            return $wallet->credit($userId, $amount, $description, 'pv_matching');

        } catch (Exception $e) {
            error_log("Credit wallet error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get PV transaction history
     */
    public function getPVHistory($userId, $limit = 50, $offset = 0) {
        $stmt = $this->db->prepare("
            SELECT pt.*, p.name as product_name
            FROM pv_transactions pt
            LEFT JOIN products p ON pt.product_id = p.id
            WHERE pt.user_id = ?
            ORDER BY pt.created_at DESC
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$userId, $limit, $offset]);
        return $stmt->fetchAll();
    }

    /**
     * Get income history
     */
    public function getIncomeHistory($userId, $limit = 50, $offset = 0) {
        $stmt = $this->db->prepare("SELECT * FROM income_logs WHERE user_id = ? ORDER BY matching_date DESC LIMIT ? OFFSET ?");
        $stmt->execute([$userId, $limit, $offset]);
        return $stmt->fetchAll();
    }

    /**
     * Get PV statistics for admin
     */
    public function getPVStatistics($dateFrom = null, $dateTo = null) {
        $whereClause = '';
        $params = [];

        if ($dateFrom && $dateTo) {
            $whereClause = 'WHERE DATE(created_at) BETWEEN ? AND ?';
            $params = [$dateFrom, $dateTo];
        }

        $stmt = $this->db->prepare("
            SELECT
                COUNT(*) as total_transactions,
                SUM(pv_amount) as total_pv,
                SUM(CASE WHEN side = 'left' THEN pv_amount ELSE 0 END) as left_pv,
                SUM(CASE WHEN side = 'right' THEN pv_amount ELSE 0 END) as right_pv,
                AVG(pv_amount) as avg_pv
            FROM pv_transactions
            $whereClause
        ");
        $stmt->execute($params);
        return $stmt->fetch();
    }

    /**
     * Run weekly PV matching for all users
     */
    public function runWeeklyMatching($weekStartDate = null, $weekEndDate = null) {
        try {
            // If no dates provided, use current week
            if (!$weekStartDate) {
                $weekStartDate = date('Y-m-d', strtotime('monday this week'));
            }
            if (!$weekEndDate) {
                $weekEndDate = date('Y-m-d', strtotime('sunday this week'));
            }

            echo "Processing weekly matching for week: {$weekStartDate} to {$weekEndDate}\n";

            $stmt = $this->db->prepare("SELECT DISTINCT user_id FROM users WHERE status = 'active'");
            $stmt->execute();
            $users = $stmt->fetchAll();

            $processed = 0;
            $totalGrossIncome = 0;
            $totalServiceCharge = 0;
            $totalTdsAmount = 0;
            $totalNetIncome = 0;
            $totalCappingApplied = 0;
            $usersWithIncome = 0;

            foreach ($users as $user) {
                $result = $this->processWeeklyPVMatching($user['user_id'], $weekStartDate, $weekEndDate);
                if ($result) {
                    $processed++;
                    if ($result['income_amount'] > 0) {
                        $usersWithIncome++;
                        $totalGrossIncome += $result['gross_income_amount'];
                        $totalServiceCharge += $result['service_charge'];
                        $totalTdsAmount += $result['tds_amount'];
                        $totalNetIncome += $result['income_amount'];
                        $totalCappingApplied += $result['capping_applied'];
                    }
                }
            }

            // Create weekly report
            $this->createWeeklyReport($weekStartDate, $weekEndDate, $usersWithIncome, $totalGrossIncome, $totalServiceCharge, $totalTdsAmount, $totalNetIncome, $totalCappingApplied);

            return [
                'processed' => $processed,
                'users_with_income' => $usersWithIncome,
                'total_gross_income' => $totalGrossIncome,
                'total_service_charge' => $totalServiceCharge,
                'total_tds_amount' => $totalTdsAmount,
                'total_income' => $totalNetIncome,
                'total_capping' => $totalCappingApplied
            ];

        } catch (Exception $e) {
            error_log("Weekly matching error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Run daily PV matching for all users (deprecated - use weekly matching)
     */
    public function runDailyMatching() {
        try {
            $stmt = $this->db->prepare("SELECT DISTINCT user_id FROM users WHERE status = 'active'");
            $stmt->execute();
            $users = $stmt->fetchAll();

            $processed = 0;
            foreach ($users as $user) {
                $this->processPVMatching($user['user_id']);
                $processed++;
            }

            return $processed;

        } catch (Exception $e) {
            error_log("Daily matching error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Process weekly PV matching for a specific user
     */
    public function processWeeklyPVMatching($userId, $weekStartDate, $weekEndDate) {
        try {
            // Check if this week has already been processed for this user
            $existingStmt = $this->db->prepare("SELECT id FROM weekly_income_logs WHERE user_id = ? AND week_start_date = ?");
            $existingStmt->execute([$userId, $weekStartDate]);
            if ($existingStmt->fetch()) {
                // Already processed this week
                return ['income_amount' => 0, 'capping_applied' => 0];
            }

            // Get available PV for this week (unused PV only)
            $availablePV = $this->getAvailablePVForWeek($userId, $weekStartDate);

            // Get previous week's carry forward
            $lastWeekStmt = $this->db->prepare("SELECT carry_forward_left, carry_forward_right FROM weekly_income_logs WHERE user_id = ? ORDER BY week_start_date DESC LIMIT 1");
            $lastWeekStmt->execute([$userId]);
            $lastWeek = $lastWeekStmt->fetch();

            $carriedLeftPV = $lastWeek['carry_forward_left'] ?? 0;
            $carriedRightPV = $lastWeek['carry_forward_right'] ?? 0;

            // Add carried forward PV to available PV
            $totalLeftPV = $availablePV['left_pv'] + $carriedLeftPV;
            $totalRightPV = $availablePV['right_pv'] + $carriedRightPV;

            // Calculate matching PV (minimum of left and right)
            $matchedPV = min($totalLeftPV, $totalRightPV);

            $grossIncomeAmount = 0;
            $serviceCharge = 0;
            $tdsAmount = 0;
            $netIncomeAmount = 0;
            $cappingApplied = 0;

            if ($matchedPV > 0) {
                // Calculate gross income (1 PV = configured rate)
                $pvRate = $this->config->getPVRate();
                $grossIncomeAmount = $matchedPV * $pvRate;

                // Apply weekly capping to gross income
                $weeklyCapping = $this->config->get('weekly_capping', 130000);

                if ($grossIncomeAmount > $weeklyCapping) {
                    $cappingApplied = $grossIncomeAmount - $weeklyCapping;
                    $grossIncomeAmount = $weeklyCapping;
                }

                // Calculate deductions
                $serviceCharge = $grossIncomeAmount * 0.10; // 10% service charge
                $tdsAmount = $grossIncomeAmount * 0.05;     // 5% TDS
                $netIncomeAmount = $grossIncomeAmount - $serviceCharge - $tdsAmount;

                // Mark PV as used for this week
                $this->markPVAsUsed($userId, $matchedPV, $weekStartDate);

                // Credit net income to wallet
                if ($netIncomeAmount > 0) {
                    $this->creditWallet($userId, $netIncomeAmount, "Weekly PV Matching Income for week {$weekStartDate} to {$weekEndDate} (Net after deductions)");
                }
            }

            // Calculate carry forward
            $carryForwardLeft = $totalLeftPV - $matchedPV;
            $carryForwardRight = $totalRightPV - $matchedPV;

            // Record weekly income log with deductions
            $this->recordWeeklyIncomeLog($userId, $weekStartDate, $weekEndDate, $totalLeftPV, $totalRightPV, $matchedPV, $grossIncomeAmount, $serviceCharge, $tdsAmount, $netIncomeAmount, $cappingApplied, $carryForwardLeft, $carryForwardRight);

            return [
                'gross_income_amount' => $grossIncomeAmount,
                'service_charge' => $serviceCharge,
                'tds_amount' => $tdsAmount,
                'income_amount' => $netIncomeAmount,
                'capping_applied' => $cappingApplied,
                'matched_pv' => $matchedPV
            ];

        } catch (Exception $e) {
            error_log("Weekly PV matching error for user $userId: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Record weekly income log
     */
    public function recordWeeklyIncomeLog($userId, $weekStartDate, $weekEndDate, $leftPV, $rightPV, $matchedPV, $grossIncomeAmount, $serviceCharge, $tdsAmount, $netIncomeAmount, $cappingApplied, $carryForwardLeft, $carryForwardRight) {
        $stmt = $this->db->prepare("INSERT INTO weekly_income_logs (user_id, week_start_date, week_end_date, left_pv, right_pv, matched_pv, gross_income_amount, service_charge, tds_amount, income_amount, weekly_capping_applied, carry_forward_left, carry_forward_right) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $userId,
            $weekStartDate,
            $weekEndDate,
            $leftPV,
            $rightPV,
            $matchedPV,
            $grossIncomeAmount,
            $serviceCharge,
            $tdsAmount,
            $netIncomeAmount,
            $cappingApplied,
            $carryForwardLeft,
            $carryForwardRight
        ]);
    }

    /**
     * Get available PV for a user for a specific week (unused PV only)
     */
    public function getAvailablePVForWeek($userId, $weekStartDate) {
        // Initialize PV usage tracking for new transactions
        $this->initializePVUsageTracking($userId);

        // Get available PV from tracking table
        $stmt = $this->db->prepare("
            SELECT
                SUM(CASE WHEN side = 'left' THEN remaining_amount ELSE 0 END) as left_pv,
                SUM(CASE WHEN side = 'right' THEN remaining_amount ELSE 0 END) as right_pv
            FROM pv_usage_tracking
            WHERE user_id = ? AND remaining_amount > 0
        ");
        $stmt->execute([$userId]);
        $result = $stmt->fetch();

        return [
            'left_pv' => $result['left_pv'] ?? 0,
            'right_pv' => $result['right_pv'] ?? 0
        ];
    }

    /**
     * Initialize PV usage tracking for new transactions
     */
    public function initializePVUsageTracking($userId) {
        // Get PV transactions that are not yet tracked
        $stmt = $this->db->prepare("
            SELECT pt.id, pt.user_id, pt.side, pt.pv_amount
            FROM pv_transactions pt
            LEFT JOIN pv_usage_tracking put ON pt.id = put.pv_transaction_id
            WHERE pt.user_id = ? AND put.id IS NULL
        ");
        $stmt->execute([$userId]);
        $untracked = $stmt->fetchAll();

        foreach ($untracked as $transaction) {
            $insertStmt = $this->db->prepare("
                INSERT INTO pv_usage_tracking (user_id, pv_transaction_id, side, original_amount, remaining_amount)
                VALUES (?, ?, ?, ?, ?)
            ");
            $insertStmt->execute([
                $transaction['user_id'],
                $transaction['id'],
                $transaction['side'],
                $transaction['pv_amount'],
                $transaction['pv_amount'] // Initially, remaining = total
            ]);
        }
    }

    /**
     * Mark PV as used for a specific week
     */
    public function markPVAsUsed($userId, $matchedPV, $weekStartDate) {
        // Get available PV transactions ordered by creation date (FIFO)
        $stmt = $this->db->prepare("
            SELECT id, side, remaining_amount
            FROM pv_usage_tracking
            WHERE user_id = ? AND remaining_amount > 0
            ORDER BY created_at ASC
        ");
        $stmt->execute([$userId]);
        $availablePV = $stmt->fetchAll();

        $leftPVToUse = $matchedPV;
        $rightPVToUse = $matchedPV;

        // Mark left side PV as used
        foreach ($availablePV as $pv) {
            if ($pv['side'] === 'left' && $leftPVToUse > 0) {
                $useAmount = min($leftPVToUse, $pv['remaining_amount']);
                $newRemaining = $pv['remaining_amount'] - $useAmount;

                $updateStmt = $this->db->prepare("
                    UPDATE pv_usage_tracking
                    SET used_amount = used_amount + ?, remaining_amount = ?, week_used = ?
                    WHERE id = ?
                ");
                $updateStmt->execute([$useAmount, $newRemaining, $weekStartDate, $pv['id']]);

                $leftPVToUse -= $useAmount;
            }
        }

        // Mark right side PV as used
        foreach ($availablePV as $pv) {
            if ($pv['side'] === 'right' && $rightPVToUse > 0) {
                $useAmount = min($rightPVToUse, $pv['remaining_amount']);
                $newRemaining = $pv['remaining_amount'] - $useAmount;

                $updateStmt = $this->db->prepare("
                    UPDATE pv_usage_tracking
                    SET used_amount = used_amount + ?, remaining_amount = ?, week_used = ?
                    WHERE id = ?
                ");
                $updateStmt->execute([$useAmount, $newRemaining, $weekStartDate, $pv['id']]);

                $rightPVToUse -= $useAmount;
            }
        }
    }

    /**
     * Create weekly report
     */
    public function createWeeklyReport($weekStartDate, $weekEndDate, $usersWithIncome, $totalGrossIncome, $totalServiceCharge, $totalTdsAmount, $totalNetIncome, $totalCappingApplied) {
        try {
            $stmt = $this->db->prepare("INSERT INTO weekly_income_reports (week_start_date, week_end_date, total_users_earned, total_gross_income, total_service_charge, total_tds_amount, total_income_distributed, total_capping_applied, report_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'generated') ON DUPLICATE KEY UPDATE total_users_earned = ?, total_gross_income = ?, total_service_charge = ?, total_tds_amount = ?, total_income_distributed = ?, total_capping_applied = ?, report_generated_at = CURRENT_TIMESTAMP");
            $stmt->execute([
                $weekStartDate,
                $weekEndDate,
                $usersWithIncome,
                $totalGrossIncome,
                $totalServiceCharge,
                $totalTdsAmount,
                $totalNetIncome,
                $totalCappingApplied,
                $usersWithIncome,
                $totalGrossIncome,
                $totalServiceCharge,
                $totalTdsAmount,
                $totalNetIncome,
                $totalCappingApplied
            ]);

            return true;
        } catch (Exception $e) {
            error_log("Create weekly report error: " . $e->getMessage());
            return false;
        }
    }
}
?>
