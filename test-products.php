<?php
/**
 * Test Products Database Connection
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Testing Products Database Connection</h2>";

try {
    // Include essential files
    require_once 'config/database.php';
    require_once 'config/Connection.php';
    
    echo "<p>✅ Database files included successfully</p>";
    
    // Get database connection
    $db = Database::getInstance();
    echo "<p>✅ Database connection established</p>";
    
    // Test products query
    $productsStmt = $db->prepare("SELECT * FROM products WHERE status = 'active' ORDER BY created_at DESC");
    $productsStmt->execute();
    $allProducts = $productsStmt->fetchAll();
    
    echo "<p>✅ Products query executed successfully</p>";
    echo "<p><strong>Total products found:</strong> " . count($allProducts) . "</p>";
    
    if (!empty($allProducts)) {
        echo "<h3>Products in database:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Code</th><th>Name</th><th>Price</th><th>PV</th><th>Status</th><th>Image</th></tr>";
        
        foreach ($allProducts as $product) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($product['id']) . "</td>";
            echo "<td>" . htmlspecialchars($product['product_code']) . "</td>";
            echo "<td>" . htmlspecialchars($product['name']) . "</td>";
            echo "<td>₹" . number_format($product['price'], 2) . "</td>";
            echo "<td>" . number_format($product['pv_value'], 2) . "</td>";
            echo "<td>" . htmlspecialchars($product['status']) . "</td>";
            echo "<td>" . htmlspecialchars($product['image'] ?? 'No image') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ No products found in database</p>";
        
        // Check if products table exists
        $tableCheck = $db->query("SHOW TABLES LIKE 'products'");
        if ($tableCheck->rowCount() > 0) {
            echo "<p>✅ Products table exists</p>";
            
            // Check total products (including inactive)
            $totalStmt = $db->query("SELECT COUNT(*) as total FROM products");
            $total = $totalStmt->fetch();
            echo "<p>Total products (all statuses): " . $total['total'] . "</p>";
            
            // Check products by status
            $statusStmt = $db->query("SELECT status, COUNT(*) as count FROM products GROUP BY status");
            $statusCounts = $statusStmt->fetchAll();
            echo "<p>Products by status:</p>";
            foreach ($statusCounts as $status) {
                echo "<p>- " . $status['status'] . ": " . $status['count'] . "</p>";
            }
        } else {
            echo "<p>❌ Products table does not exist</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
}
?>

<p><a href="index.php">← Back to Homepage</a></p>
