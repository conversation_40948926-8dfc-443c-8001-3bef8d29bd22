Stack trace:
Frame         Function      Args
0007FFFFB750  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA650) msys-2.0.dll+0x1FE8E
0007FFFFB750  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA28) msys-2.0.dll+0x67F9
0007FFFFB750  000210046832 (000210286019, 0007FFFFB608, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB750  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB750  000210068E24 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA30  00021006A225 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB72030000 ntdll.dll
7FFB70E60000 KERNEL32.DLL
7FFB6F860000 KERNELBASE.dll
7FFB71840000 USER32.dll
7FFB6F450000 win32u.dll
7FFB70B30000 GDI32.dll
7FFB6F690000 gdi32full.dll
7FFB6F7C0000 msvcp_win.dll
7FFB6F570000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB71AB0000 advapi32.dll
7FFB71A00000 msvcrt.dll
7FFB71F40000 sechost.dll
7FFB6F540000 bcrypt.dll
7FFB70F90000 RPCRT4.dll
7FFB6E780000 CRYPTBASE.DLL
7FFB6FC40000 bcryptPrimitives.dll
7FFB710C0000 IMM32.DLL
