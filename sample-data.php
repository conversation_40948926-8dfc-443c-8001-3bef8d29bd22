<?php
/**
 * Sample Data Insertion Script
 * Creates sample products for testing the dynamic home page
 */

require_once 'config/database.php';
require_once 'config/Connection.php';

try {
    $db = Database::getInstance();
    
    // Sample products data
    $sampleProducts = [
        [
            'product_code' => 'GSD001',
            'name' => 'Gas Safety Device Pro',
            'description' => 'Advanced gas safety device with automatic shut-off and leak detection. Protects your family from gas accidents with 25-year life cycle and 5-year warranty.',
            'price' => 2500.00,
            'pv_value' => 250.00,
            'status' => 'active'
        ],
        [
            'product_code' => 'GLD002',
            'name' => 'Smart Gas Leak Detector',
            'description' => 'Digital gas leak detector with smart alerts and mobile app integration. Real-time monitoring for complete safety.',
            'price' => 1800.00,
            'pv_value' => 180.00,
            'status' => 'active'
        ],
        [
            'product_code' => 'SSM003',
            'name' => 'Safety Sensor Monitor',
            'description' => 'Professional safety monitoring system for industrial and commercial use. Heavy-duty protection with automatic alerts.',
            'price' => 3200.00,
            'pv_value' => 320.00,
            'status' => 'active'
        ],
        [
            'product_code' => 'HSC004',
            'name' => 'Home Safety Controller',
            'description' => 'Domestic safety controller for kitchen and household gas appliances. Easy installation and user-friendly interface.',
            'price' => 1500.00,
            'pv_value' => 150.00,
            'status' => 'active'
        ],
        [
            'product_code' => 'IGS005',
            'name' => 'Industrial Gas Guard',
            'description' => 'Heavy-duty industrial gas safety system for commercial establishments. Professional-grade protection with business warranty.',
            'price' => 4500.00,
            'pv_value' => 450.00,
            'status' => 'active'
        ],
        [
            'product_code' => 'ASD006',
            'name' => 'Automatic Safety Device',
            'description' => 'Electronic automatic safety device with smart technology. Digital monitoring and protection for modern homes.',
            'price' => 2200.00,
            'pv_value' => 220.00,
            'status' => 'active'
        ],
        [
            'product_code' => 'GCM007',
            'name' => 'Gas Cylinder Monitor',
            'description' => 'LPG cylinder monitoring device that indicates low gas levels and detects leakage. Perfect for domestic use.',
            'price' => 1200.00,
            'pv_value' => 120.00,
            'status' => 'active'
        ],
        [
            'product_code' => 'PSS008',
            'name' => 'Professional Safety Shield',
            'description' => 'Professional-grade safety shield for commercial kitchens and industrial gas systems. Maximum protection guaranteed.',
            'price' => 3800.00,
            'pv_value' => 380.00,
            'status' => 'active'
        ],
        [
            'product_code' => 'SGA009',
            'name' => 'Smart Gas Alarm',
            'description' => 'Smart gas alarm system with digital alerts and automatic emergency response. Tech-enabled safety solution.',
            'price' => 1600.00,
            'pv_value' => 160.00,
            'status' => 'active'
        ],
        [
            'product_code' => 'HKS010',
            'name' => 'Home Kitchen Safety',
            'description' => 'Complete kitchen safety solution for household gas stoves and burners. Family protection made simple.',
            'price' => 1100.00,
            'pv_value' => 110.00,
            'status' => 'active'
        ]
    ];
    
    // Check if products already exist
    $existingStmt = $db->query("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
    $existingCount = $existingStmt->fetch()['count'];
    
    if ($existingCount > 0) {
        echo "<h2>Sample Data Status</h2>\n";
        echo "<p>✅ Database already contains {$existingCount} active products.</p>\n";
        echo "<p>🏠 <a href='index.php'>View Home Page</a> | 📦 <a href='products.php'>View Products</a></p>\n";
        exit;
    }
    
    // Get admin ID (assuming first admin exists)
    $adminStmt = $db->query("SELECT id FROM admin LIMIT 1");
    $admin = $adminStmt->fetch();
    $adminId = $admin ? $admin['id'] : 1;
    
    // Insert sample products
    $insertStmt = $db->prepare("
        INSERT INTO products (product_code, name, description, price, pv_value, status, created_by) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");
    
    $insertedCount = 0;
    foreach ($sampleProducts as $product) {
        try {
            $insertStmt->execute([
                $product['product_code'],
                $product['name'],
                $product['description'],
                $product['price'],
                $product['pv_value'],
                $product['status'],
                $adminId
            ]);
            $insertedCount++;
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                echo "⚠️ Product {$product['product_code']} already exists, skipping...\n";
            } else {
                echo "❌ Error inserting {$product['product_code']}: " . $e->getMessage() . "\n";
            }
        }
    }
    
    // Create some sample purchase orders for best-selling products
    $userStmt = $db->query("SELECT user_id FROM users WHERE status = 'active' LIMIT 3");
    $users = $userStmt->fetchAll();
    
    if (!empty($users)) {
        $orderStmt = $db->prepare("
            INSERT INTO purchase_orders (order_id, user_id, product_id, quantity, total_amount, pv_amount, placement_side, payment_status, order_status) 
            VALUES (?, ?, ?, ?, ?, ?, ?, 'completed', 'confirmed')
        ");
        
        // Get some product IDs
        $productStmt = $db->query("SELECT id, price, pv_value FROM products WHERE status = 'active' LIMIT 5");
        $products = $productStmt->fetchAll();
        
        $orderCount = 0;
        foreach ($products as $product) {\n            foreach ($users as $user) {
                $orderId = 'ORD' . time() . rand(100, 999);
                $quantity = rand(1, 3);
                $totalAmount = $product['price'] * $quantity;
                $pvAmount = $product['pv_value'] * $quantity;
                $side = rand(0, 1) ? 'left' : 'right';
                
                try {
                    $orderStmt->execute([
                        $orderId,
                        $user['user_id'],
                        $product['id'],
                        $quantity,
                        $totalAmount,
                        $pvAmount,
                        $side
                    ]);
                    $orderCount++;
                } catch (PDOException $e) {
                    // Skip if error
                }
                
                if ($orderCount >= 10) break 2; // Limit to 10 orders
            }
        }
        
        echo "<p>✅ Created {$orderCount} sample purchase orders for best-selling analytics.</p>\n";
    }
    
    echo "<h2>✅ Sample Data Inserted Successfully!</h2>\n";
    echo "<p>📊 Inserted {$insertedCount} sample products into the database.</p>\n";
    echo "<p>🎯 Your dynamic home page is now ready with:</p>\n";
    echo "<ul>\n";
    echo "<li>✨ Featured Products</li>\n";
    echo "<li>🏆 Best Selling Products</li>\n";
    echo "<li>📂 Dynamic Categories</li>\n";
    echo "<li>🔥 Deal of the Day</li>\n";
    echo "<li>📈 Real-time Analytics</li>\n";
    echo "</ul>\n";
    echo "<p>🏠 <a href='index.php' style='background: #059669; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Your Dynamic Home Page</a></p>\n";
    echo "<p>📦 <a href='products.php' style='background: #0891b2; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Browse Products</a></p>\n";
    
} catch (PDOException $e) {
    echo "<h2>❌ Error</h2>\n";
    echo "<p>Database error: " . $e->getMessage() . "</p>\n";
    echo "<p>Please make sure your database is properly configured and the setup.php script has been run.</p>\n";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample Data - ShaktiPure</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        h2 { color: #059669; }
        .success { color: #059669; }
        .error { color: #dc2626; }
        .warning { color: #d97706; }
        ul { background: #f0fdf4; padding: 20px; border-radius: 8px; }
        a { margin: 10px; display: inline-block; }
    </style>
</head>
<body>
    <h1>🏠 ShaktiPure Dynamic Home Page Setup</h1>
    <p>This script creates sample product data to demonstrate the dynamic home page functionality.</p>
</body>
</html>
