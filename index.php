<?php
/**
 * Main Index Page - Modern E-commerce Homepage
 * ShaktiPure Industries
 */

// Enable error reporting in development
if (!defined('ENVIRONMENT') || ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// Include essential files
require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/session.php';
require_once 'includes/functions.php';
require_once 'includes/FileUpload.php';

// Check if user is already logged in and redirect accordingly
if (isLoggedIn()) {
    $userType = getCurrentUserType();
    switch ($userType) {
        case 'admin':
            header("Location: admin/dashboard.php");
            break;
        case 'franchise':
            header("Location: franchise/dashboard.php");
            break;
        case 'user':
            header("Location: user/dashboard.php");
            break;
        default:
            destroyUserSession();
            break;
    }
    exit();
}

// Get all active products for homepage
$db = Database::getInstance();
$productsStmt = $db->prepare("SELECT * FROM products WHERE status = 'active' ORDER BY created_at DESC");
$productsStmt->execute();
$allProducts = $productsStmt->fetchAll();

// Get product count for display
$productCount = count($allProducts);

// For homepage, show first 12 products with option to view all
$homepageProductLimit = 12;
$displayProducts = array_slice($allProducts, 0, $homepageProductLimit);
$hasMoreProducts = $productCount > $homepageProductLimit;

$fileUpload = new FileUpload();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Shaktipure Industries Pvt Ltd is a leading manufacturer and exporter of high-quality water purifiers, water treatment plants, and water testing equipment. We offer a wide range of products for both residential and commercial use, ensuring safe and clean drinking water for all.">
    <title>Shaktipure Industries Pvt Ltd</title>
    <link rel="icon" type="image/png" href="assets/images/onlylogo.png">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="assets/css/homepage.css" rel="stylesheet">
</head>
<body>
    <!-- Top Header -->
    <div class="top-header">
        <div class="container">
            <div class="top-header-left">
                🔥 Welcome to Shaktipure Industries PVT. LTD.
            </div>
            <div class="top-header-right">
                <a href="tel:+91-9876543210"><i class="fas fa-phone"></i> +91-**********</a>
                <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL>  </a>
            </div>
        </div>
    </div>

    <!-- Main Header -->
    <header class="main-header">
        <div class="container">
            <div class="header-content">
                    <img src="assets/images/logo.png" alt="ShaktiPure" style="height: 40px; margin-right: 10px;">
                <div class="search-bar">
                    <form action="products.php" method="GET">
                        <input type="text" name="search" placeholder="Search for products, brands and more...">
                        <button type="submit"><i class="fas fa-search"></i></button>
                    </form>
                </div>

                <div class="header-actions">
                    <a href="user/login.php" class="header-btn">
                        <i class="fas fa-user"></i>
                        <span>Account</span>
                    </a>
                    <a href="#" class="header-btn">
                        <i class="fas fa-heart"></i>
                        <span>Wishlist</span>
                    </a>
                    <a href="#" class="header-btn">
                        <i class="fas fa-shopping-cart"></i>
                        <span>Cart</span>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="navigation">
        <div class="container">
            <div class="nav-content">
                <button class="categories-btn">
                    <i class="fas fa-bars"></i>
                    PRODUCT CATEGORIES
                </button>
                <ul class="nav-menu">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="#categories">Categories</a></li>
                    <li><a href="#products">Products</a></li>
                    <li><a href="#">About</a></li>
                    <li><a href="#">Contact</a></li>
                    <li><a href="user/register.php">Register</a></li>
                    <li><a href="user/login.php">Login</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <div class="hero-subtitle">Next Generation</div>
                    <h1>Water Purfier</h1>
                    <p class="hero-description">
                    Experience the future of clean living with our advanced water purification system. Monitor, manage, and enjoy pure, healthy water anytime — right from your dashboard
                    </p>
                    <div class="hero-buttons">
                        <a href="#products" class="btn-primary-hero">
                            <i class="fas fa-shopping-cart"></i>
                            Shop Now
                        </a>
                        <a href="#categories" class="btn-secondary-hero">See More</a>
                    </div>
                </div>
                <div class="hero-image">
                    <img src="assets/images/1.png" alt="VR Headset" style="max-width: 250px;">
                </div>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="categories-section" id="categories">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Choose your Category</h2>
                <p class="section-subtitle">
                    Discover our wide range of premium tech products and accessories
                </p>
            </div>
            <div class="categories-grid">
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-vr-cardboard"></i>
                    </div>
                    <h3>Virtual Reality</h3>
                    <p>Premium VR headsets</p>
                </div>
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-headphones"></i>
                    </div>
                    <h3>Audio</h3>
                    <p>High-quality headphones</p>
                </div>
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-gamepad"></i>
                    </div>
                    <h3>Gaming</h3>
                    <p>Gaming accessories</p>
                </div>
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>Mobile</h3>
                    <p>Smartphones & tablets</p>
                </div>
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-laptop"></i>
                    </div>
                    <h3>Computers</h3>
                    <p>Laptops & accessories</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section class="products-section" id="products">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Our Products</h2>
                <p class="section-subtitle">
                    Discover our complete range of premium products (<?php echo $productCount; ?> products available)
                </p>
            </div>
            <div class="products-grid">
                <?php if (!empty($displayProducts)): ?>
                    <?php foreach ($displayProducts as $product): ?>
                        <div class="product-card">
                            <div class="product-image">
                                <?php if (!empty($product['image'])): ?>
                                    <img src="<?php echo htmlspecialchars($fileUpload->getFileUrl($product['image'])); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                <?php else: ?>
                                    <img src="assets/images/placeholder-product.jpg" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                <?php endif; ?>
                                <div class="product-badge">New</div>
                            </div>
                            <div class="product-info">
                                <div class="product-code"><?php echo htmlspecialchars($product['product_code']); ?></div>
                                <h3 class="product-title"><?php echo htmlspecialchars($product['name']); ?></h3>
                                <p class="product-description"><?php echo htmlspecialchars(substr($product['description'], 0, 100)); ?>...</p>
                                <div class="product-price">
                                    <span class="current-price">₹<?php echo number_format($product['price'], 2); ?></span>
                                    <span class="pv-value"><?php echo number_format($product['pv_value'], 2); ?> PV</span>
                                </div>
                                <div class="product-actions">
                                    <button class="btn-add-cart">Add to Cart</button>
                                    <button class="btn-wishlist"><i class="fas fa-heart"></i></button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-box-open fa-4x text-muted mb-3" style="color: var(--text-gray-light);"></i>
                        <h4 style="color: var(--text-white);">No Products Available</h4>
                        <p style="color: var(--text-gray-light);">No products found in the database. Please add products through the admin panel.</p>
                        <a href="admin/products.php" class="btn-primary-hero mt-3">
                            <i class="fas fa-plus"></i>
                            Add Products
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <?php if ($hasMoreProducts): ?>
                <div class="text-center mt-5">
                    <a href="products.php" class="btn-primary-hero">
                        <i class="fas fa-th-large"></i>
                        View All <?php echo $productCount; ?> Products
                    </a>
                    <p class="mt-3 text-center" style="color: var(--text-gray-light);">
                        Showing <?php echo count($displayProducts); ?> of <?php echo $productCount; ?> products
                    </p>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Shaktipure</h3>
                    <p>Your trusted partner for cutting-edge technology and premium Water Purfier.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Contact</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                        <li><a href="#">FAQ</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="#">Gallery</a></li>
                        <li><a href="#">Legal Documents</a></li>
                        <li><a href="#">Our bankers</a></li>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">Awards</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <ul>
                        <li><i class="fas fa-phone"></i> +91-**********</li>
                        <li><i class="fas fa-envelope"></i> <EMAIL></li>
                        <li><i class="fas fa-map-marker-alt"></i> Shakti Pure Industries Pvt Ltd, D-224, Udhana Complex, Udhana, Surat-394210, Gujarat, India</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 ShaktiPure Industries. All rights reserved. | Designed By Abhishek sharma </p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Categories button functionality
        document.querySelector('.categories-btn').addEventListener('click', function() {
            alert('Product categories feature coming soon!');
        });

        // Add to cart functionality
        document.querySelectorAll('.btn-add-cart').forEach(button => {
            button.addEventListener('click', function() {
                alert('Add to cart functionality will be implemented soon!');
            });
        });

        // Wishlist functionality
        document.querySelectorAll('.btn-wishlist').forEach(button => {
            button.addEventListener('click', function() {
                this.style.color = this.style.color === 'red' ? '' : 'red';
            });
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Celio website loaded successfully!');
        });
    </script>
</body>
</html>
