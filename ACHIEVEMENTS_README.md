# User Achievement System

This document describes the newly implemented user achievement system that allows administrators to assign levels and awards to users.

## Features

### Admin Features
- **User Level Management**: Assign levels (Beginner, Intermediate, Expert) to users
- **Award System**: Assign awards like "Top Performer", "Employee of the Month", etc.
- **Achievement History**: Track all level and award changes with admin notes
- **Bulk Operations**: Manage multiple users efficiently
- **Statistics Dashboard**: View achievement distribution and recent changes

### User Features
- **Achievement Display**: View current level and awards on dashboard and profile
- **Visual Indicators**: Attractive badges and icons for levels and awards
- **Achievement History**: Users can see their achievement progression (via admin interface)

## Database Schema

### New Columns in `users` table:
- `user_level`: ENUM('Beginner', 'Intermediate', 'Expert') DEFAULT 'Beginner'
- `current_award`: VARCHAR(100) NULL

### New `user_achievements` table:
- `id`: Primary key
- `user_id`: Reference to users table
- `achievement_type`: ENUM('level', 'award')
- `previous_value`: Previous level/award value
- `new_value`: New level/award value
- `assigned_by`: Admin who made the change
- `admin_notes`: Optional notes from admin
- `created_at`: Timestamp of change

## Available Levels
1. **Beginner** - Default level for new users
2. **Intermediate** - Mid-level users
3. **Expert** - Advanced users

## Available Awards
- Top Performer
- Employee of the Month
- Outstanding Contributor
- Rising Star
- Team Leader
- Excellence Award
- Innovation Award
- Customer Champion

## Installation

### For New Installations
The achievement system is automatically included when running `setup.php`.

### For Existing Installations
Run the migration script to add achievement features:

```bash
php migrate-achievements.php
```

## Admin Usage

### Accessing Achievement Management
1. Login to admin panel
2. Navigate to "Achievements" in the main menu
3. Use the interface to assign levels and awards

### Assigning Levels
1. Find the user in the achievements page
2. Click the level button (📈)
3. Select new level and add optional notes
4. Submit the form

### Assigning Awards
1. Find the user in the achievements page
2. Click the award button (🏆)
3. Select award and add optional notes
4. Submit the form

### Removing Awards
1. Find a user with an award
2. Click the remove button (❌)
3. Add reason for removal
4. Confirm removal

### Viewing Achievement History
1. Click the history button (📜) for any user
2. View chronological list of all changes
3. See admin notes and timestamps

## User Experience

### Dashboard Display
Users see their achievements prominently displayed on their dashboard with:
- Current level with colored badge
- Current award (if any) with trophy icon
- Motivational messages for users without awards

### Profile Display
Similar achievement display on the profile page with:
- Visual achievement icons
- Level and award badges
- Congratulatory or motivational messages

## Security Features

### Admin-Only Access
- Only administrators can modify user achievements
- All changes are logged with admin ID and timestamp
- CSRF protection on all forms

### Audit Trail
- Complete history of all achievement changes
- Admin notes for each change
- Timestamps for accountability

## Styling

### CSS Classes
- `.level-badge`: Styled badges for user levels
- `.award-badge`: Styled badges for awards
- `.achievement-card`: Cards for displaying achievements
- `.achievement-icon`: Circular icons for achievements
- `.timeline`: Achievement history timeline

### Color Scheme
- **Beginner**: Green gradient
- **Intermediate**: Orange gradient  
- **Expert**: Red gradient
- **Awards**: Purple gradient with gold icons

## API Integration

### UserAchievement Model Methods
- `assignLevel($userId, $level, $adminId, $notes)`: Assign level to user
- `assignAward($userId, $award, $adminId, $notes)`: Assign award to user
- `removeAward($userId, $adminId, $notes)`: Remove user's award
- `getUserAchievementHistory($userId, $limit)`: Get user's achievement history
- `getAchievementStats()`: Get system-wide achievement statistics

## File Structure

```
/admin/
  ├── user-achievements.php          # Main achievement management page
  └── ajax/
      └── get-achievement-history.php # AJAX endpoint for history

/models/
  └── UserAchievement.php            # Achievement model class

/assets/css/
  └── achievements.css               # Achievement styling

/user/
  ├── dashboard.php                  # Updated with achievement display
  └── profile.php                    # Updated with achievement display

migrate-achievements.php             # Migration script for existing installations
ACHIEVEMENTS_README.md              # This documentation file
```

## Customization

### Adding New Levels
1. Update the ENUM in the database schema
2. Add the new level to `UserAchievement::LEVELS` array
3. Add corresponding CSS classes in `achievements.css`

### Adding New Awards
1. Add the award name to `UserAchievement::AWARDS` array
2. No database changes needed

### Styling Customization
Modify `assets/css/achievements.css` to change:
- Badge colors and gradients
- Icon styles and sizes
- Animation effects
- Responsive behavior

## Troubleshooting

### Common Issues

**Migration fails with foreign key error:**
- Ensure admin table exists before running migration
- Check database user has ALTER privileges

**Achievements not displaying:**
- Verify CSS file is included in page headers
- Check browser console for JavaScript errors
- Ensure user has achievements assigned

**AJAX history not loading:**
- Check admin authentication
- Verify AJAX endpoint path is correct
- Check browser network tab for errors

### Support
For technical support or feature requests, contact the development team.
